"use client";

import { useEffect, useState, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { CheckCircle, ArrowRight, Home, CreditCard } from "lucide-react";
import Link from "next/link";
import { subscriptionService } from "@/services/subscriptionService";

function SuccessPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isLoading, setIsLoading] = useState(true);
  const [subscriptionData, setSubscriptionData] = useState(null);
  const [error, setError] = useState(null);

  const sessionId = searchParams.get('session_id');

  useEffect(() => {
    const verifyPayment = async () => {
      if (!sessionId) {
        setError("ID da sessão não encontrado");
        setIsLoading(false);
        return;
      }

      try {
        // Aguarda um pouco para garantir que o webhook foi processado
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Busca os dados da assinatura
        const subscription = await subscriptionService.getSubscription();
        setSubscriptionData(subscription);
      } catch (error) {
        console.error("Erro ao verificar pagamento:", error);
        setError("Erro ao verificar o status do pagamento");
      } finally {
        setIsLoading(false);
      }
    };

    verifyPayment();
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Verificando pagamento...
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Aguarde enquanto confirmamos sua assinatura.
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CreditCard className="w-6 h-6 text-red-600" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Erro na verificação
          </h2>
          <p className="text-gray-600 mb-6">
            {error}
          </p>
          <div className="space-y-3">
            <Link
              href="/subscription/signup"
              className="block w-full bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 transition-colors"
            >
              Tentar novamente
            </Link>
            <Link
              href="/landing"
              className="block w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Voltar ao início
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 max-w-2xl w-full">
        {/* Success Icon */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="w-10 h-10 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Pagamento realizado com sucesso!
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Bem-vindo ao High Tide Systems! Sua assinatura está ativa.
          </p>
        </div>

        {/* Subscription Details */}
        {subscriptionData && (
          <div className="bg-gray-50 rounded-xl p-6 mb-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Detalhes da sua assinatura
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Plano</p>
                <p className="font-medium text-gray-900">Plano Básico</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Ciclo de faturamento</p>
                <p className="font-medium text-gray-900">
                  {subscriptionData.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <p className="font-medium text-green-600">Ativo</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Próximo pagamento</p>
                <p className="font-medium text-gray-900">
                  {subscriptionData.nextBillingDate 
                    ? new Date(subscriptionData.nextBillingDate).toLocaleDateString('pt-BR')
                    : 'Em processamento'
                  }
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Next Steps */}
        <div className="bg-orange-50 border border-orange-200 rounded-xl p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Próximos passos
          </h3>
          <div className="space-y-3">
            <div className="flex items-start">
              <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                1
              </div>
              <div>
                <p className="font-medium text-gray-900">Acesse sua conta</p>
                <p className="text-sm text-gray-600">
                  Faça login com suas credenciais para começar a usar o sistema.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                2
              </div>
              <div>
                <p className="font-medium text-gray-900">Configure sua empresa</p>
                <p className="text-sm text-gray-600">
                  Complete as informações da sua empresa e personalize o sistema.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                3
              </div>
              <div>
                <p className="font-medium text-gray-900">Comece a agendar</p>
                <p className="text-sm text-gray-600">
                  Cadastre seus pacientes e comece a gerenciar seus agendamentos.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Link
            href="/login"
            className="flex-1 inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-500 hover:bg-orange-600 transition-colors"
          >
            Acessar minha conta
            <ArrowRight className="w-5 h-5 ml-2" />
          </Link>
          <Link
            href="/landing"
            className="flex-1 inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <Home className="w-5 h-5 mr-2" />
            Voltar ao início
          </Link>
        </div>

        {/* Support Info */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            Precisa de ajuda? Entre em contato conosco pelo email{" "}
            <a href="mailto:<EMAIL>" className="text-orange-600 hover:text-orange-700">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default function SubscriptionSuccessPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Carregando...
          </h2>
        </div>
      </div>
    }>
      <SuccessPageContent />
    </Suspense>
  );
}
