/** @type {import('next').NextConfig} */
const nextConfig = {
  // Habilitar output standalone para Docker
  output: 'standalone',

  // Configurar redirecionamentos para API se necessário
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/:path*`
      }
    ]
  },

  // Configuração do webpack para resolver problemas com FullCalendar
  webpack: (config, { isServer }) => {
    // Resolve the @fullcalendar/core/preact.js issue
    config.resolve.alias = {
      ...config.resolve.alias,
      '@fullcalendar/core/preact.js': 'preact',
    };

    return config;
  },
}

export default nextConfig;