"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stackblur-canvas";
exports.ids = ["vendor-chunks/stackblur-canvas"];
exports.modules = {

/***/ "(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js":
/*!************************************************************!*\
  !*** ./node_modules/stackblur-canvas/dist/stackblur-es.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlurStack: () => (/* binding */ BlurStack),\n/* harmony export */   canvasRGB: () => (/* binding */ processCanvasRGB),\n/* harmony export */   canvasRGBA: () => (/* binding */ processCanvasRGBA),\n/* harmony export */   image: () => (/* binding */ processImage),\n/* harmony export */   imageDataRGB: () => (/* binding */ processImageDataRGB),\n/* harmony export */   imageDataRGBA: () => (/* binding */ processImageDataRGBA)\n/* harmony export */ });\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n\n  return _typeof(obj);\n}\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\n/* eslint-disable no-bitwise -- used for calculations */\n\n/* eslint-disable unicorn/prefer-query-selector -- aiming at\n  backward-compatibility */\n\n/**\n* StackBlur - a fast almost Gaussian Blur For Canvas\n*\n* In case you find this class useful - especially in commercial projects -\n* I am not totally unhappy for a small donation to my PayPal account\n* <EMAIL>\n*\n* Or support me on flattr:\n* {@link https://flattr.com/thing/72791/StackBlur-a-fast-almost-Gaussian-Blur-Effect-for-CanvasJavascript}.\n*\n* @module StackBlur\n* <AUTHOR> Klingemann\n* Contact: <EMAIL>\n* Website: {@link http://www.quasimondo.com/StackBlurForCanvas/StackBlurDemo.html}\n* Twitter: @quasimondo\n*\n* @copyright (c) 2010 Mario Klingemann\n*\n* Permission is hereby granted, free of charge, to any person\n* obtaining a copy of this software and associated documentation\n* files (the \"Software\"), to deal in the Software without\n* restriction, including without limitation the rights to use,\n* copy, modify, merge, publish, distribute, sublicense, and/or sell\n* copies of the Software, and to permit persons to whom the\n* Software is furnished to do so, subject to the following\n* conditions:\n*\n* The above copyright notice and this permission notice shall be\n* included in all copies or substantial portions of the Software.\n*\n* THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\n* EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES\n* OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\n* NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT\n* HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,\n* WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n* FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR\n* OTHER DEALINGS IN THE SOFTWARE.\n*/\nvar mulTable = [512, 512, 456, 512, 328, 456, 335, 512, 405, 328, 271, 456, 388, 335, 292, 512, 454, 405, 364, 328, 298, 271, 496, 456, 420, 388, 360, 335, 312, 292, 273, 512, 482, 454, 428, 405, 383, 364, 345, 328, 312, 298, 284, 271, 259, 496, 475, 456, 437, 420, 404, 388, 374, 360, 347, 335, 323, 312, 302, 292, 282, 273, 265, 512, 497, 482, 468, 454, 441, 428, 417, 405, 394, 383, 373, 364, 354, 345, 337, 328, 320, 312, 305, 298, 291, 284, 278, 271, 265, 259, 507, 496, 485, 475, 465, 456, 446, 437, 428, 420, 412, 404, 396, 388, 381, 374, 367, 360, 354, 347, 341, 335, 329, 323, 318, 312, 307, 302, 297, 292, 287, 282, 278, 273, 269, 265, 261, 512, 505, 497, 489, 482, 475, 468, 461, 454, 447, 441, 435, 428, 422, 417, 411, 405, 399, 394, 389, 383, 378, 373, 368, 364, 359, 354, 350, 345, 341, 337, 332, 328, 324, 320, 316, 312, 309, 305, 301, 298, 294, 291, 287, 284, 281, 278, 274, 271, 268, 265, 262, 259, 257, 507, 501, 496, 491, 485, 480, 475, 470, 465, 460, 456, 451, 446, 442, 437, 433, 428, 424, 420, 416, 412, 408, 404, 400, 396, 392, 388, 385, 381, 377, 374, 370, 367, 363, 360, 357, 354, 350, 347, 344, 341, 338, 335, 332, 329, 326, 323, 320, 318, 315, 312, 310, 307, 304, 302, 299, 297, 294, 292, 289, 287, 285, 282, 280, 278, 275, 273, 271, 269, 267, 265, 263, 261, 259];\nvar shgTable = [9, 11, 12, 13, 13, 14, 14, 15, 15, 15, 15, 16, 16, 16, 16, 17, 17, 17, 17, 17, 17, 17, 18, 18, 18, 18, 18, 18, 18, 18, 18, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 19, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 22, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 23, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24];\n/**\n * @param {string|HTMLImageElement} img\n * @param {string|HTMLCanvasElement} canvas\n * @param {Float} radius\n * @param {boolean} blurAlphaChannel\n * @param {boolean} useOffset\n * @param {boolean} skipStyles\n * @returns {undefined}\n */\n\nfunction processImage(img, canvas, radius, blurAlphaChannel, useOffset, skipStyles) {\n  if (typeof img === 'string') {\n    img = document.getElementById(img);\n  }\n\n  if (!img || Object.prototype.toString.call(img).slice(8, -1) === 'HTMLImageElement' && !('naturalWidth' in img)) {\n    return;\n  }\n\n  var dimensionType = useOffset ? 'offset' : 'natural';\n  var w = img[dimensionType + 'Width'];\n  var h = img[dimensionType + 'Height']; // add ImageBitmap support,can blur texture source\n\n  if (Object.prototype.toString.call(img).slice(8, -1) === 'ImageBitmap') {\n    w = img.width;\n    h = img.height;\n  }\n\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || !('getContext' in canvas)) {\n    return;\n  }\n\n  if (!skipStyles) {\n    canvas.style.width = w + 'px';\n    canvas.style.height = h + 'px';\n  }\n\n  canvas.width = w;\n  canvas.height = h;\n  var context = canvas.getContext('2d');\n  context.clearRect(0, 0, w, h);\n  context.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight, 0, 0, w, h);\n\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  if (blurAlphaChannel) {\n    processCanvasRGBA(canvas, 0, 0, w, h, radius);\n  } else {\n    processCanvasRGB(canvas, 0, 0, w, h, radius);\n  }\n}\n/**\n * @param {string|HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @throws {Error|TypeError}\n * @returns {ImageData} See {@link https://html.spec.whatwg.org/multipage/canvas.html#imagedata}\n */\n\n\nfunction getImageDataFromCanvas(canvas, topX, topY, width, height) {\n  if (typeof canvas === 'string') {\n    canvas = document.getElementById(canvas);\n  }\n\n  if (!canvas || _typeof(canvas) !== 'object' || !('getContext' in canvas)) {\n    throw new TypeError('Expecting canvas with `getContext` method ' + 'in processCanvasRGB(A) calls!');\n  }\n\n  var context = canvas.getContext('2d');\n\n  try {\n    return context.getImageData(topX, topY, width, height);\n  } catch (e) {\n    throw new Error('unable to access image data: ' + e);\n  }\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGBA(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGBA(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGBA(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null,\n      stackOut = null,\n      yw = 0,\n      yi = 0;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n\n  for (var y = 0; y < height; y++) {\n    stack = stackStart;\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        pa = pixels[yi + 3];\n\n    for (var _i = 0; _i < radiusPlus1; _i++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack.a = pa;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0,\n        aInSum = 0,\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        aOutSum = radiusPlus1 * pa,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb,\n        aSum = sumFactor * pa;\n\n    for (var _i2 = 1; _i2 < radiusPlus1; _i2++) {\n      var p = yi + ((widthMinus1 < _i2 ? widthMinus1 : _i2) << 2);\n      var r = pixels[p],\n          g = pixels[p + 1],\n          b = pixels[p + 2],\n          a = pixels[p + 3];\n      var rbs = radiusPlus1 - _i2;\n      rSum += (stack.r = r) * rbs;\n      gSum += (stack.g = g) * rbs;\n      bSum += (stack.b = b) * rbs;\n      aSum += (stack.a = a) * rbs;\n      rInSum += r;\n      gInSum += g;\n      bInSum += b;\n      aInSum += a;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      var paInitial = aSum * mulSum >>> shgSum;\n      pixels[yi + 3] = paInitial;\n\n      if (paInitial !== 0) {\n        var _a2 = 255 / paInitial;\n\n        pixels[yi] = (rSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 1] = (gSum * mulSum >>> shgSum) * _a2;\n        pixels[yi + 2] = (bSum * mulSum >>> shgSum) * _a2;\n      } else {\n        pixels[yi] = pixels[yi + 1] = pixels[yi + 2] = 0;\n      }\n\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      aSum -= aOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      aOutSum -= stackIn.a;\n\n      var _p = x + radius + 1;\n\n      _p = yw + (_p < widthMinus1 ? _p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[_p];\n      gInSum += stackIn.g = pixels[_p + 1];\n      bInSum += stackIn.b = pixels[_p + 2];\n      aInSum += stackIn.a = pixels[_p + 3];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      aSum += aInSum;\n      stackIn = stackIn.next;\n      var _stackOut = stackOut,\n          _r = _stackOut.r,\n          _g = _stackOut.g,\n          _b = _stackOut.b,\n          _a = _stackOut.a;\n      rOutSum += _r;\n      gOutSum += _g;\n      bOutSum += _b;\n      aOutSum += _a;\n      rInSum -= _r;\n      gInSum -= _g;\n      bInSum -= _b;\n      aInSum -= _a;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x = 0; _x < width; _x++) {\n    yi = _x << 2;\n\n    var _pr = pixels[yi],\n        _pg = pixels[yi + 1],\n        _pb = pixels[yi + 2],\n        _pa = pixels[yi + 3],\n        _rOutSum = radiusPlus1 * _pr,\n        _gOutSum = radiusPlus1 * _pg,\n        _bOutSum = radiusPlus1 * _pb,\n        _aOutSum = radiusPlus1 * _pa,\n        _rSum = sumFactor * _pr,\n        _gSum = sumFactor * _pg,\n        _bSum = sumFactor * _pb,\n        _aSum = sumFactor * _pa;\n\n    stack = stackStart;\n\n    for (var _i3 = 0; _i3 < radiusPlus1; _i3++) {\n      stack.r = _pr;\n      stack.g = _pg;\n      stack.b = _pb;\n      stack.a = _pa;\n      stack = stack.next;\n    }\n\n    var yp = width;\n    var _gInSum = 0,\n        _bInSum = 0,\n        _aInSum = 0,\n        _rInSum = 0;\n\n    for (var _i4 = 1; _i4 <= radius; _i4++) {\n      yi = yp + _x << 2;\n\n      var _rbs = radiusPlus1 - _i4;\n\n      _rSum += (stack.r = _pr = pixels[yi]) * _rbs;\n      _gSum += (stack.g = _pg = pixels[yi + 1]) * _rbs;\n      _bSum += (stack.b = _pb = pixels[yi + 2]) * _rbs;\n      _aSum += (stack.a = _pa = pixels[yi + 3]) * _rbs;\n      _rInSum += _pr;\n      _gInSum += _pg;\n      _bInSum += _pb;\n      _aInSum += _pa;\n      stack = stack.next;\n\n      if (_i4 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y = 0; _y < height; _y++) {\n      var _p2 = yi << 2;\n\n      pixels[_p2 + 3] = _pa = _aSum * mulSum >>> shgSum;\n\n      if (_pa > 0) {\n        _pa = 255 / _pa;\n        pixels[_p2] = (_rSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 1] = (_gSum * mulSum >>> shgSum) * _pa;\n        pixels[_p2 + 2] = (_bSum * mulSum >>> shgSum) * _pa;\n      } else {\n        pixels[_p2] = pixels[_p2 + 1] = pixels[_p2 + 2] = 0;\n      }\n\n      _rSum -= _rOutSum;\n      _gSum -= _gOutSum;\n      _bSum -= _bOutSum;\n      _aSum -= _aOutSum;\n      _rOutSum -= stackIn.r;\n      _gOutSum -= stackIn.g;\n      _bOutSum -= stackIn.b;\n      _aOutSum -= stackIn.a;\n      _p2 = _x + ((_p2 = _y + radiusPlus1) < heightMinus1 ? _p2 : heightMinus1) * width << 2;\n      _rSum += _rInSum += stackIn.r = pixels[_p2];\n      _gSum += _gInSum += stackIn.g = pixels[_p2 + 1];\n      _bSum += _bInSum += stackIn.b = pixels[_p2 + 2];\n      _aSum += _aInSum += stackIn.a = pixels[_p2 + 3];\n      stackIn = stackIn.next;\n      _rOutSum += _pr = stackOut.r;\n      _gOutSum += _pg = stackOut.g;\n      _bOutSum += _pb = stackOut.b;\n      _aOutSum += _pa = stackOut.a;\n      _rInSum -= _pr;\n      _gInSum -= _pg;\n      _bInSum -= _pb;\n      _aInSum -= _pa;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {undefined}\n */\n\n\nfunction processCanvasRGB(canvas, topX, topY, width, height, radius) {\n  if (isNaN(radius) || radius < 1) {\n    return;\n  }\n\n  radius |= 0;\n  var imageData = getImageDataFromCanvas(canvas, topX, topY, width, height);\n  imageData = processImageDataRGB(imageData, topX, topY, width, height, radius);\n  canvas.getContext('2d').putImageData(imageData, topX, topY);\n}\n/**\n * @param {ImageData} imageData\n * @param {Integer} topX\n * @param {Integer} topY\n * @param {Integer} width\n * @param {Integer} height\n * @param {Float} radius\n * @returns {ImageData}\n */\n\n\nfunction processImageDataRGB(imageData, topX, topY, width, height, radius) {\n  var pixels = imageData.data;\n  var div = 2 * radius + 1; // const w4 = width << 2;\n\n  var widthMinus1 = width - 1;\n  var heightMinus1 = height - 1;\n  var radiusPlus1 = radius + 1;\n  var sumFactor = radiusPlus1 * (radiusPlus1 + 1) / 2;\n  var stackStart = new BlurStack();\n  var stack = stackStart;\n  var stackEnd;\n\n  for (var i = 1; i < div; i++) {\n    stack = stack.next = new BlurStack();\n\n    if (i === radiusPlus1) {\n      stackEnd = stack;\n    }\n  }\n\n  stack.next = stackStart;\n  var stackIn = null;\n  var stackOut = null;\n  var mulSum = mulTable[radius];\n  var shgSum = shgTable[radius];\n  var p, rbs;\n  var yw = 0,\n      yi = 0;\n\n  for (var y = 0; y < height; y++) {\n    var pr = pixels[yi],\n        pg = pixels[yi + 1],\n        pb = pixels[yi + 2],\n        rOutSum = radiusPlus1 * pr,\n        gOutSum = radiusPlus1 * pg,\n        bOutSum = radiusPlus1 * pb,\n        rSum = sumFactor * pr,\n        gSum = sumFactor * pg,\n        bSum = sumFactor * pb;\n    stack = stackStart;\n\n    for (var _i5 = 0; _i5 < radiusPlus1; _i5++) {\n      stack.r = pr;\n      stack.g = pg;\n      stack.b = pb;\n      stack = stack.next;\n    }\n\n    var rInSum = 0,\n        gInSum = 0,\n        bInSum = 0;\n\n    for (var _i6 = 1; _i6 < radiusPlus1; _i6++) {\n      p = yi + ((widthMinus1 < _i6 ? widthMinus1 : _i6) << 2);\n      rSum += (stack.r = pr = pixels[p]) * (rbs = radiusPlus1 - _i6);\n      gSum += (stack.g = pg = pixels[p + 1]) * rbs;\n      bSum += (stack.b = pb = pixels[p + 2]) * rbs;\n      rInSum += pr;\n      gInSum += pg;\n      bInSum += pb;\n      stack = stack.next;\n    }\n\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var x = 0; x < width; x++) {\n      pixels[yi] = rSum * mulSum >>> shgSum;\n      pixels[yi + 1] = gSum * mulSum >>> shgSum;\n      pixels[yi + 2] = bSum * mulSum >>> shgSum;\n      rSum -= rOutSum;\n      gSum -= gOutSum;\n      bSum -= bOutSum;\n      rOutSum -= stackIn.r;\n      gOutSum -= stackIn.g;\n      bOutSum -= stackIn.b;\n      p = yw + ((p = x + radius + 1) < widthMinus1 ? p : widthMinus1) << 2;\n      rInSum += stackIn.r = pixels[p];\n      gInSum += stackIn.g = pixels[p + 1];\n      bInSum += stackIn.b = pixels[p + 2];\n      rSum += rInSum;\n      gSum += gInSum;\n      bSum += bInSum;\n      stackIn = stackIn.next;\n      rOutSum += pr = stackOut.r;\n      gOutSum += pg = stackOut.g;\n      bOutSum += pb = stackOut.b;\n      rInSum -= pr;\n      gInSum -= pg;\n      bInSum -= pb;\n      stackOut = stackOut.next;\n      yi += 4;\n    }\n\n    yw += width;\n  }\n\n  for (var _x2 = 0; _x2 < width; _x2++) {\n    yi = _x2 << 2;\n\n    var _pr2 = pixels[yi],\n        _pg2 = pixels[yi + 1],\n        _pb2 = pixels[yi + 2],\n        _rOutSum2 = radiusPlus1 * _pr2,\n        _gOutSum2 = radiusPlus1 * _pg2,\n        _bOutSum2 = radiusPlus1 * _pb2,\n        _rSum2 = sumFactor * _pr2,\n        _gSum2 = sumFactor * _pg2,\n        _bSum2 = sumFactor * _pb2;\n\n    stack = stackStart;\n\n    for (var _i7 = 0; _i7 < radiusPlus1; _i7++) {\n      stack.r = _pr2;\n      stack.g = _pg2;\n      stack.b = _pb2;\n      stack = stack.next;\n    }\n\n    var _rInSum2 = 0,\n        _gInSum2 = 0,\n        _bInSum2 = 0;\n\n    for (var _i8 = 1, yp = width; _i8 <= radius; _i8++) {\n      yi = yp + _x2 << 2;\n      _rSum2 += (stack.r = _pr2 = pixels[yi]) * (rbs = radiusPlus1 - _i8);\n      _gSum2 += (stack.g = _pg2 = pixels[yi + 1]) * rbs;\n      _bSum2 += (stack.b = _pb2 = pixels[yi + 2]) * rbs;\n      _rInSum2 += _pr2;\n      _gInSum2 += _pg2;\n      _bInSum2 += _pb2;\n      stack = stack.next;\n\n      if (_i8 < heightMinus1) {\n        yp += width;\n      }\n    }\n\n    yi = _x2;\n    stackIn = stackStart;\n    stackOut = stackEnd;\n\n    for (var _y2 = 0; _y2 < height; _y2++) {\n      p = yi << 2;\n      pixels[p] = _rSum2 * mulSum >>> shgSum;\n      pixels[p + 1] = _gSum2 * mulSum >>> shgSum;\n      pixels[p + 2] = _bSum2 * mulSum >>> shgSum;\n      _rSum2 -= _rOutSum2;\n      _gSum2 -= _gOutSum2;\n      _bSum2 -= _bOutSum2;\n      _rOutSum2 -= stackIn.r;\n      _gOutSum2 -= stackIn.g;\n      _bOutSum2 -= stackIn.b;\n      p = _x2 + ((p = _y2 + radiusPlus1) < heightMinus1 ? p : heightMinus1) * width << 2;\n      _rSum2 += _rInSum2 += stackIn.r = pixels[p];\n      _gSum2 += _gInSum2 += stackIn.g = pixels[p + 1];\n      _bSum2 += _bInSum2 += stackIn.b = pixels[p + 2];\n      stackIn = stackIn.next;\n      _rOutSum2 += _pr2 = stackOut.r;\n      _gOutSum2 += _pg2 = stackOut.g;\n      _bOutSum2 += _pb2 = stackOut.b;\n      _rInSum2 -= _pr2;\n      _gInSum2 -= _pg2;\n      _bInSum2 -= _pb2;\n      stackOut = stackOut.next;\n      yi += width;\n    }\n  }\n\n  return imageData;\n}\n/**\n *\n */\n\n\nvar BlurStack =\n/**\n * Set properties.\n */\nfunction BlurStack() {\n  _classCallCheck(this, BlurStack);\n\n  this.r = 0;\n  this.g = 0;\n  this.b = 0;\n  this.a = 0;\n  this.next = null;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stackblur-canvas/dist/stackblur-es.js\n");

/***/ })

};
;