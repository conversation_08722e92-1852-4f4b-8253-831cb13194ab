"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ModuleCard */ \"(app-pages-browser)/./src/components/ui/ModuleCard.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _subscription_modules;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showToast } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModuleModal, setShowModuleModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n                loadCompanies();\n            } else {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (selectedCompanyId) {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId\n    ]);\n    const loadCompanies = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get('/admin/companies');\n            setCompanies(response.data.companies || []);\n        } catch (error) {\n            console.error('Erro ao carregar empresas:', error);\n            showToast('Erro ao carregar lista de empresas', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadData = async ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && !selectedCompanyId) {\n            return;\n        }\n        try {\n            setIsLoading(true);\n            // Para SYSTEM_ADMIN, adiciona o companyId como query parameter\n            const queryParams = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && selectedCompanyId ? \"?companyId=\".concat(selectedCompanyId) : '';\n            const [subscriptionData, plansData, invoicesData] = await Promise.all([\n                _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/subscription\".concat(queryParams)).then((res)=>res.data),\n                _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.getPlans(),\n                _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/invoices\".concat(queryParams)).then((res)=>res.data)\n            ]);\n            setSubscription(subscriptionData);\n            setPlans(plansData);\n            setInvoices((invoicesData === null || invoicesData === void 0 ? void 0 : invoicesData.invoices) || invoicesData || []);\n        } catch (error) {\n            console.error('Erro ao carregar dados:', error);\n            showToast('Erro ao carregar informações do plano', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.addModule(moduleType);\n            showToast('Módulo adicionado com sucesso!', 'success');\n            await loadData();\n            setShowModuleModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao adicionar módulo:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao adicionar módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.removeModule(moduleType);\n            showToast('Módulo removido com sucesso!', 'success');\n            await loadData();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao remover módulo:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao remover módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.cancelSubscription();\n            showToast('Assinatura cancelada com sucesso!', 'success');\n            await loadData();\n            setShowCancelModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao cancelar assinatura:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao cancelar assinatura', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'text-green-600 bg-green-100';\n            case 'TRIAL':\n                return 'text-blue-600 bg-blue-100';\n            case 'PAST_DUE':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'CANCELED':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'Ativo';\n            case 'TRIAL':\n                return 'Período de Teste';\n            case 'PAST_DUE':\n                return 'Pagamento em Atraso';\n            case 'CANCELED':\n                return 'Cancelado';\n            default:\n                return status;\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('pt-BR');\n    };\n    const formatCurrency = (value)=>{\n        return new Intl.NumberFormat('pt-BR', {\n            style: 'currency',\n            currency: 'BRL'\n        }).format(value);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-admin-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Se não há assinatura (SYSTEM_ADMIN sem empresa específica)\n    if (!subscription && (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    subtitle: \"Gerencie sua assinatura, m\\xf3dulos e faturamento\",\n                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    moduleColor: \"admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    moduleColor: \"admin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto w-16 h-16 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-8 w-8 text-admin-primary\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                children: \"Nenhuma Assinatura Encontrada\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                children: \"Como administrador do sistema, voc\\xea tem acesso total, mas n\\xe3o h\\xe1 uma assinatura espec\\xedfica associada \\xe0 sua conta.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                children: \"Para gerenciar planos de uma empresa espec\\xedfica, acesse atrav\\xe9s da conta de administrador da empresa.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                subtitle: \"Gerencie sua assinatura, m\\xf3dulos e faturamento\",\n                icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                moduleColor: \"admin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: \"Status da Assinatura\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                        children: getStatusText(subscription.status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Ciclo de Faturamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: subscription.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Valor Mensal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: formatCurrency(subscription.pricePerMonth)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Pr\\xf3ximo Pagamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Data de In\\xedcio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: formatDate(subscription.startDate)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, undefined),\n                            subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 266,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                            children: \"Sua assinatura ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 265,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 264,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6 text-admin-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 277,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"M\\xf3dulos Ativos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 280,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-admin-primary mb-4\",\n                                    children: ((_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.filter((m)=>m.active).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowModuleModal(true),\n                                    moduleColor: \"admin\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Gerenciar M\\xf3dulos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 275,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 225,\n                columnNumber: 9\n            }, undefined),\n            (subscription === null || subscription === void 0 ? void 0 : subscription.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"M\\xf3dulos da Assinatura\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 304,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                onClick: ()=>setShowModuleModal(true),\n                                moduleColor: \"admin\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Adicionar M\\xf3dulo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 303,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: subscription.modules.map((module)=>{\n                            var _plans_modules_module_moduleType, _plans_modules;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border rounded-lg \".concat(module.active ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: (plans === null || plans === void 0 ? void 0 : (_plans_modules = plans.modules) === null || _plans_modules === void 0 ? void 0 : (_plans_modules_module_moduleType = _plans_modules[module.moduleType]) === null || _plans_modules_module_moduleType === void 0 ? void 0 : _plans_modules_module_moduleType.name) || module.moduleType\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            module.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 332,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 334,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                        children: [\n                                            formatCurrency(module.pricePerMonth),\n                                            \"/m\\xeas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    module.active && ![\n                                        'BASIC',\n                                        'ADMIN',\n                                        'SCHEDULING'\n                                    ].includes(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleRemoveModule(module.moduleType),\n                                        disabled: actionLoading,\n                                        className: \"w-full text-red-600 border-red-200 hover:bg-red-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 348,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Remover\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 341,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, module.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 319,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 317,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 302,\n                columnNumber: 9\n            }, undefined),\n            invoices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"Hist\\xf3rico de Faturas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: loadData,\n                                disabled: isLoading,\n                                moduleColor: \"admin\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(isLoading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Atualizar\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 361,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Valor\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Vencimento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"A\\xe7\\xf5es\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 393,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 380,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 379,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-gray-100 dark:border-gray-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatCurrency(invoice.amount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 404,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(invoice.status === 'PAID' ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20' : invoice.status === 'PENDING' ? 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20' : 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'),\n                                                        children: invoice.status === 'PAID' ? 'Pago' : invoice.status === 'PENDING' ? 'Pendente' : 'Falhou'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.dueDate)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-right\",\n                                                    children: invoice.stripeInvoiceUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: invoice.stripeInvoiceUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"inline-flex items-center text-admin-primary hover:text-admin-primary/80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            \"Download\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, invoice.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 400,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 398,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 378,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 377,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 360,\n                columnNumber: 9\n            }, undefined),\n            subscription && subscription.status === 'ACTIVE' && !subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Gerenciar Assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 448,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Cancele sua assinatura ou fa\\xe7a altera\\xe7\\xf5es no seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 451,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 447,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                            variant: \"outline\",\n                            onClick: ()=>setShowCancelModal(true),\n                            className: \"text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 460,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Cancelar Assinatura\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 455,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 446,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 445,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showCancelModal,\n                onClose: ()=>setShowCancelModal(false),\n                title: \"Cancelar Assinatura\",\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 476,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                            children: \"Aten\\xe7\\xe3o!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                            children: \"Ao cancelar, voc\\xea perder\\xe1 acesso aos recursos pagos no final do per\\xedodo atual.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 475,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Tem certeza de que deseja cancelar sua assinatura? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 487,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(false),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: \"Manter Assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 492,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: handleCancelSubscription,\n                                    disabled: actionLoading,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 508,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Cancelar Assinatura\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 491,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 474,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 468,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showModuleModal,\n                onClose: ()=>setShowModuleModal(false),\n                title: \"Gerenciar M\\xf3dulos\",\n                moduleColor: \"admin\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Adicione ou remova m\\xf3dulos da sua assinatura. Os m\\xf3dulos b\\xe1sicos n\\xe3o podem ser removidos.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 525,\n                            columnNumber: 11\n                        }, undefined),\n                        (plans === null || plans === void 0 ? void 0 : plans.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: Object.entries(plans.modules).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                var _subscription_modules;\n                                const isActive = subscription === null || subscription === void 0 ? void 0 : (_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.find((m)=>m.moduleType === moduleType && m.active);\n                                const isBasic = [\n                                    'BASIC',\n                                    'ADMIN',\n                                    'SCHEDULING'\n                                ].includes(moduleType);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg \".concat(isActive ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: moduleInfo.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 544,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 551,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                    children: [\n                                                        formatCurrency(moduleInfo.monthlyPrice),\n                                                        \"/m\\xeas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                !isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                                    size: \"sm\",\n                                                    variant: isActive ? \"outline\" : \"default\",\n                                                    onClick: ()=>isActive ? handleRemoveModule(moduleType) : handleAddModule(moduleType),\n                                                    disabled: actionLoading,\n                                                    moduleColor: \"admin\",\n                                                    className: isActive ? \"text-red-600 border-red-200 hover:bg-red-50\" : \"\",\n                                                    children: actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 570,\n                                                        columnNumber: 29\n                                                    }, undefined) : isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Remover\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Adicionar\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"Inclu\\xeddo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 555,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 536,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 524,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 517,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"FYFw7ke5hAgRFzLok0vR+p2JFGk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = PlansPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});