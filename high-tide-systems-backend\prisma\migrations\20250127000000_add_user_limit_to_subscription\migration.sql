-- Add userLimit field to Subscription table
ALTER TABLE "Subscription" ADD COLUMN "userLimit" INTEGER NOT NULL DEFAULT 50;

-- Update existing subscriptions with appropriate user limits based on their pricing
UPDATE "Subscription" 
SET "userLimit" = CASE 
  WHEN "pricePerMonth" <= 99.90 THEN 5
  WHEN "pricePerMonth" <= 199.90 THEN 20
  WHEN "pricePerMonth" <= 349.90 THEN 50
  WHEN "pricePerMonth" <= 599.90 THEN 100
  ELSE 200
END;
