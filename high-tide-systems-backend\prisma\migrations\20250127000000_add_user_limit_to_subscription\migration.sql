-- Add userLimit field to Subscription table
ALTER TABLE "Subscription" ADD COLUMN "userLimit" INTEGER NOT NULL DEFAULT 50;

-- Update existing subscriptions with calculated user limits based on their pricing
-- Assumindo preço base de R$ 19.90 por usuário e aplicando a lógica de desconto reversa
UPDATE "Subscription"
SET "userLimit" = CASE
  -- Para preços muito baixos (planos básicos), definir um mínimo
  WHEN "pricePerMonth" <= 99.90 THEN GREATEST(ROUND("pricePerMonth" / 19.90), 5)
  -- Para outros preços, calcular baseado no preço dividido pelo valor base
  -- Considerando os descontos progressivos do sistema
  ELSE GREATEST(ROUND("pricePerMonth" / 15.92), 10) -- 15.92 é aproximadamente 19.90 com desconto médio
END;
