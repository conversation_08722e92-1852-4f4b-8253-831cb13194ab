"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ModuleCard */ \"(app-pages-browser)/./src/components/ui/ModuleCard.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _subscription_modules;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showToast } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModuleModal, setShowModuleModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n                loadCompanies();\n            } else {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (selectedCompanyId) {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId\n    ]);\n    const loadCompanies = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get('/companies?limit=100');\n            setCompanies(response.data.companies || []);\n        } catch (error) {\n            console.error('Erro ao carregar empresas:', error);\n            showToast('Erro ao carregar lista de empresas', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadData = async ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && !selectedCompanyId) {\n            return;\n        }\n        try {\n            setIsLoading(true);\n            // Para SYSTEM_ADMIN, adiciona o companyId como query parameter\n            const queryParams = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && selectedCompanyId ? \"?companyId=\".concat(selectedCompanyId) : '';\n            const [subscriptionData, plansData, invoicesData] = await Promise.all([\n                _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/subscription\".concat(queryParams)).then((res)=>res.data),\n                _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.getPlans(),\n                _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/invoices\".concat(queryParams)).then((res)=>res.data)\n            ]);\n            setSubscription(subscriptionData);\n            setPlans(plansData);\n            setInvoices((invoicesData === null || invoicesData === void 0 ? void 0 : invoicesData.invoices) || invoicesData || []);\n        } catch (error) {\n            console.error('Erro ao carregar dados:', error);\n            showToast('Erro ao carregar informações do plano', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.addModule(moduleType);\n            showToast('Módulo adicionado com sucesso!', 'success');\n            await loadData();\n            setShowModuleModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao adicionar módulo:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao adicionar módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.removeModule(moduleType);\n            showToast('Módulo removido com sucesso!', 'success');\n            await loadData();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao remover módulo:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao remover módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.cancelSubscription();\n            showToast('Assinatura cancelada com sucesso!', 'success');\n            await loadData();\n            setShowCancelModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao cancelar assinatura:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao cancelar assinatura', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'text-green-600 bg-green-100';\n            case 'TRIAL':\n                return 'text-blue-600 bg-blue-100';\n            case 'PAST_DUE':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'CANCELED':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'Ativo';\n            case 'TRIAL':\n                return 'Período de Teste';\n            case 'PAST_DUE':\n                return 'Pagamento em Atraso';\n            case 'CANCELED':\n                return 'Cancelado';\n            default:\n                return status;\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('pt-BR');\n    };\n    const formatCurrency = (value)=>{\n        return new Intl.NumberFormat('pt-BR', {\n            style: 'currency',\n            currency: 'BRL'\n        }).format(value);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-admin-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 178,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 177,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Se for SYSTEM_ADMIN, mostrar seletor de empresa\n    if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n        var _subscription_modules1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    subtitle: \"Selecione uma empresa para gerenciar sua assinatura, m\\xf3dulos e faturamento\",\n                    icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                    moduleColor: \"admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    moduleColor: \"admin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Selecionar Empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                        value: selectedCompanyId || '',\n                                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                                        moduleColor: \"admin\",\n                                        className: \"w-full max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione uma empresa...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: company.id,\n                                                    children: company.name\n                                                }, company.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 200,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, undefined),\n                            !selectedCompanyId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-admin-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 217,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"Selecione uma empresa acima para visualizar e gerenciar seus planos de assinatura.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, undefined),\n                            selectedCompanyId && !subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-yellow-600 dark:text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                        children: \"Nenhuma Assinatura Encontrada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"A empresa selecionada n\\xe3o possui uma assinatura ativa no momento.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 227,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, undefined),\n                selectedCompanyId && subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: \"Status da Assinatura\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                                children: getStatusText(subscription.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 252,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"Ciclo de Faturamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: subscription.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"Valor Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: formatCurrency(subscription.pricePerMonth)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 264,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3ximo Pagamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 270,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"Data de In\\xedcio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: formatDate(subscription.startDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 257,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                    children: \"A assinatura desta empresa ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 288,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 286,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 285,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-6 w-6 text-admin-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 299,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"M\\xf3dulos Ativos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 301,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-admin-primary mb-4\",\n                                            children: ((_subscription_modules1 = subscription.modules) === null || _subscription_modules1 === void 0 ? void 0 : _subscription_modules1.filter((m)=>m.active).length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setShowModuleModal(true),\n                                            moduleColor: \"admin\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Gerenciar M\\xf3dulos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 246,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                subtitle: \"Gerencie sua assinatura, m\\xf3dulos e faturamento\",\n                icon: _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                moduleColor: \"admin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, undefined),\n            subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: \"Status da Assinatura\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                        children: getStatusText(subscription.status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Ciclo de Faturamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 349,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: subscription.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 350,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Valor Mensal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 355,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: formatCurrency(subscription.pricePerMonth)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 354,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Pr\\xf3ximo Pagamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 362,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 360,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Data de In\\xedcio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: formatDate(subscription.startDate)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, undefined),\n                            subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 377,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                            children: \"Sua assinatura ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 378,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 375,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6 text-admin-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 388,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"M\\xf3dulos Ativos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 391,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-admin-primary mb-4\",\n                                    children: ((_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.filter((m)=>m.active).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowModuleModal(true),\n                                    moduleColor: \"admin\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Gerenciar M\\xf3dulos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 336,\n                columnNumber: 9\n            }, undefined),\n            (subscription === null || subscription === void 0 ? void 0 : subscription.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"M\\xf3dulos da Assinatura\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                onClick: ()=>setShowModuleModal(true),\n                                moduleColor: \"admin\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 423,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Adicionar M\\xf3dulo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: subscription.modules.map((module)=>{\n                            var _plans_modules_module_moduleType, _plans_modules;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border rounded-lg \".concat(module.active ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: (plans === null || plans === void 0 ? void 0 : (_plans_modules = plans.modules) === null || _plans_modules === void 0 ? void 0 : (_plans_modules_module_moduleType = _plans_modules[module.moduleType]) === null || _plans_modules_module_moduleType === void 0 ? void 0 : _plans_modules_module_moduleType.name) || module.moduleType\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 439,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            module.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 443,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 445,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 438,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                        children: [\n                                            formatCurrency(module.pricePerMonth),\n                                            \"/m\\xeas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 448,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    module.active && ![\n                                        'BASIC',\n                                        'ADMIN',\n                                        'SCHEDULING'\n                                    ].includes(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleRemoveModule(module.moduleType),\n                                        disabled: actionLoading,\n                                        className: \"w-full text-red-600 border-red-200 hover:bg-red-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 459,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Remover\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 452,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, module.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 430,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 428,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, undefined),\n            invoices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"Hist\\xf3rico de Faturas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 473,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: loadData,\n                                disabled: isLoading,\n                                moduleColor: \"admin\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(isLoading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 483,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Atualizar\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 476,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 472,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 492,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Valor\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 498,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Vencimento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"A\\xe7\\xf5es\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 504,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 491,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 490,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-gray-100 dark:border-gray-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatCurrency(invoice.amount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(invoice.status === 'PAID' ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20' : invoice.status === 'PENDING' ? 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20' : 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'),\n                                                        children: invoice.status === 'PAID' ? 'Pago' : invoice.status === 'PENDING' ? 'Pendente' : 'Falhou'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.dueDate)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-right\",\n                                                    children: invoice.stripeInvoiceUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: invoice.stripeInvoiceUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"inline-flex items-center text-admin-primary hover:text-admin-primary/80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            \"Download\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 535,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, invoice.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 511,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 488,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 471,\n                columnNumber: 9\n            }, undefined),\n            subscription && subscription.status === 'ACTIVE' && !subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Gerenciar Assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 559,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Cancele sua assinatura ou fa\\xe7a altera\\xe7\\xf5es no seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 558,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                            variant: \"outline\",\n                            onClick: ()=>setShowCancelModal(true),\n                            className: \"text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 571,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Cancelar Assinatura\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 566,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 557,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 556,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showCancelModal,\n                onClose: ()=>setShowCancelModal(false),\n                title: \"Cancelar Assinatura\",\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                            children: \"Aten\\xe7\\xe3o!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 589,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                            children: \"Ao cancelar, voc\\xea perder\\xe1 acesso aos recursos pagos no final do per\\xedodo atual.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 588,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 586,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Tem certeza de que deseja cancelar sua assinatura? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(false),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: \"Manter Assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: handleCancelSubscription,\n                                    disabled: actionLoading,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 617,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 619,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Cancelar Assinatura\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 611,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 602,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 585,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 579,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showModuleModal,\n                onClose: ()=>setShowModuleModal(false),\n                title: \"Gerenciar M\\xf3dulos\",\n                moduleColor: \"admin\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Adicione ou remova m\\xf3dulos da sua assinatura. Os m\\xf3dulos b\\xe1sicos n\\xe3o podem ser removidos.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 636,\n                            columnNumber: 11\n                        }, undefined),\n                        (plans === null || plans === void 0 ? void 0 : plans.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: Object.entries(plans.modules).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                var _subscription_modules;\n                                const isActive = subscription === null || subscription === void 0 ? void 0 : (_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.find((m)=>m.moduleType === moduleType && m.active);\n                                const isBasic = [\n                                    'BASIC',\n                                    'ADMIN',\n                                    'SCHEDULING'\n                                ].includes(moduleType);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg \".concat(isActive ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: moduleInfo.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 659,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 655,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 662,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                    children: [\n                                                        formatCurrency(moduleInfo.monthlyPrice),\n                                                        \"/m\\xeas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                !isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                                    size: \"sm\",\n                                                    variant: isActive ? \"outline\" : \"default\",\n                                                    onClick: ()=>isActive ? handleRemoveModule(moduleType) : handleAddModule(moduleType),\n                                                    disabled: actionLoading,\n                                                    moduleColor: \"admin\",\n                                                    className: isActive ? \"text-red-600 border-red-200 hover:bg-red-50\" : \"\",\n                                                    children: actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 29\n                                                    }, undefined) : isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 684,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Remover\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 689,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Adicionar\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"Inclu\\xeddo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 697,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 666,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 647,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 641,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 635,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 628,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 326,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"FYFw7ke5hAgRFzLok0vR+p2JFGk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = PlansPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});