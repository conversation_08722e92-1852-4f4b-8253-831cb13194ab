'use client';

import { motion } from 'framer-motion';
import { 
  DollarSign, 
  Receipt, 
  Users2, 
  Package, 
  ShoppingCart, 
  FileText,
  Clock,
  Sparkles
} from 'lucide-react';

const FutureImplementations = () => {
  const futureModules = [
    {
      id: 'financial',
      icon: <DollarSign size={32} />,
      title: 'Financeiro',
      description: 'Controle financeiro completo com fluxo de caixa, contas a pagar e receber.',
      features: [
        'Controle de receitas e despesas',
        'Fluxo de caixa em tempo real',
        'Contas a pagar e receber',
        'Conciliação bancária',
        'Relatórios financeiros avançados'
      ],
      color: 'emerald'
    },
    {
      id: 'billing',
      icon: <Receipt size={32} />,
      title: 'Faturamento',
      description: 'Sistema completo de faturamento para convênios e particulares.',
      features: [
        'Faturamento automático de convênios',
        'Emissão de notas fiscais',
        'Controle de guias e autorizações',
        'Integração com operadoras',
        'Relatórios de faturamento'
      ],
      color: 'blue'
    },
    {
      id: 'hr',
      icon: <Users2 size={32} />,
      title: 'Recursos Humanos',
      description: 'Gestão completa de colaboradores, folha de pagamento e benefícios.',
      features: [
        'Cadastro de funcionários',
        'Controle de ponto e frequência',
        'Folha de pagamento',
        'Gestão de benefícios',
        'Avaliações de desempenho'
      ],
      color: 'purple'
    },
    {
      id: 'inventory',
      icon: <Package size={32} />,
      title: 'Estoque',
      description: 'Controle de estoque de medicamentos, materiais e equipamentos.',
      features: [
        'Controle de entrada e saída',
        'Gestão de fornecedores',
        'Alertas de estoque mínimo',
        'Controle de validade',
        'Relatórios de movimentação'
      ],
      color: 'amber'
    },
    {
      id: 'purchasing',
      icon: <ShoppingCart size={32} />,
      title: 'Compras',
      description: 'Sistema de compras integrado com controle de pedidos e fornecedores.',
      features: [
        'Solicitações de compra',
        'Cotações de fornecedores',
        'Controle de pedidos',
        'Aprovações de compra',
        'Integração com estoque'
      ],
      color: 'orange'
    },
    {
      id: 'protocols',
      icon: <FileText size={32} />,
      title: 'Protocolos (ABA+)',
      description: 'Protocolos especializados baseados em ABA para terapias comportamentais.',
      features: [
        'Protocolos ABA personalizados',
        'Acompanhamento de metas',
        'Relatórios de progresso',
        'Análise comportamental',
        'Integração com agendamentos'
      ],
      color: 'indigo'
    }
  ];

  return (
    <section id="future-implementations" className="py-16 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-center mb-4">
            <Sparkles className="h-8 w-8 text-orange-600 dark:text-orange-400 mr-3" />
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
              Implementações Futuras
            </h2>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-4">
            Estamos constantemente evoluindo! Conheça as funcionalidades que estão sendo desenvolvidas para tornar o High Tide Systems ainda mais completo.
          </p>
          <div className="flex items-center justify-center text-sm text-gray-500 dark:text-gray-400">
            <Clock className="h-4 w-4 mr-2" />
            <span>Em desenvolvimento - Lançamento previsto para os próximos meses</span>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {futureModules.map((module, index) => (
            <motion.div
              key={module.id}
              className="bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className={`p-6 ${
                module.color === 'emerald' ? 'bg-emerald-50 dark:bg-emerald-900/20' :
                module.color === 'blue' ? 'bg-blue-50 dark:bg-blue-900/20' :
                module.color === 'purple' ? 'bg-purple-50 dark:bg-purple-900/20' :
                module.color === 'amber' ? 'bg-amber-50 dark:bg-amber-900/20' :
                module.color === 'orange' ? 'bg-orange-50 dark:bg-orange-900/20' :
                module.color === 'indigo' ? 'bg-indigo-50 dark:bg-indigo-900/20' :
                'bg-gray-50 dark:bg-gray-900/20'
              }`}>
                <div className="flex items-center mb-4">
                  <div className={`w-16 h-16 rounded-lg ${
                    module.color === 'emerald' ? 'bg-emerald-100 dark:bg-emerald-800/30' :
                    module.color === 'blue' ? 'bg-blue-100 dark:bg-blue-800/30' :
                    module.color === 'purple' ? 'bg-purple-100 dark:bg-purple-800/30' :
                    module.color === 'amber' ? 'bg-amber-100 dark:bg-amber-800/30' :
                    module.color === 'orange' ? 'bg-orange-100 dark:bg-orange-800/30' :
                    module.color === 'indigo' ? 'bg-indigo-100 dark:bg-indigo-800/30' :
                    'bg-gray-100 dark:bg-gray-800/30'
                  } flex items-center justify-center mr-4`}>
                    <div className={
                      module.color === 'emerald' ? 'text-emerald-600 dark:text-emerald-400' :
                      module.color === 'blue' ? 'text-blue-600 dark:text-blue-400' :
                      module.color === 'purple' ? 'text-purple-600 dark:text-purple-400' :
                      module.color === 'amber' ? 'text-amber-600 dark:text-amber-400' :
                      module.color === 'orange' ? 'text-orange-600 dark:text-orange-400' :
                      module.color === 'indigo' ? 'text-indigo-600 dark:text-indigo-400' :
                      'text-gray-600 dark:text-gray-400'
                    }>
                      {module.icon}
                    </div>
                  </div>
                  <div>
                    <h3 className={`text-xl font-bold ${
                      module.color === 'emerald' ? 'text-emerald-800 dark:text-emerald-300' :
                      module.color === 'blue' ? 'text-blue-800 dark:text-blue-300' :
                      module.color === 'purple' ? 'text-purple-800 dark:text-purple-300' :
                      module.color === 'amber' ? 'text-amber-800 dark:text-amber-300' :
                      module.color === 'orange' ? 'text-orange-800 dark:text-orange-300' :
                      module.color === 'indigo' ? 'text-indigo-800 dark:text-indigo-300' :
                      'text-gray-800 dark:text-gray-300'
                    }`}>
                      {module.title}
                    </h3>
                    <div className="flex items-center mt-1">
                      <Clock className="h-3 w-3 text-gray-400 mr-1" />
                      <span className="text-xs text-gray-500 dark:text-gray-400">Em desenvolvimento</span>
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                  {module.description}
                </p>

                <div className="space-y-2">
                  {module.features.slice(0, 3).map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start">
                      <div className={`mt-1 mr-3 w-4 h-4 rounded-full flex items-center justify-center ${
                        module.color === 'emerald' ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-600 dark:text-emerald-400' :
                        module.color === 'blue' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' :
                        module.color === 'purple' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400' :
                        module.color === 'amber' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-600 dark:text-amber-400' :
                        module.color === 'orange' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400' :
                        module.color === 'indigo' ? 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-600 dark:text-indigo-400' :
                        'bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400'
                      }`}>
                        <div className="w-1.5 h-1.5 rounded-full bg-current"></div>
                      </div>
                      <span className="text-gray-600 dark:text-gray-300 text-sm">{feature}</span>
                    </div>
                  ))}
                  {module.features.length > 3 && (
                    <div className="text-xs text-gray-500 dark:text-gray-400 ml-7">
                      +{module.features.length - 3} funcionalidades adicionais
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg p-8 border border-gray-200 dark:border-gray-700 max-w-2xl mx-auto">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              Quer ser notificado sobre novos lançamentos?
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Cadastre-se para receber atualizações sobre as novas funcionalidades assim que forem lançadas.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Seu e-mail"
                className="flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-gray-900 dark:text-white bg-white dark:bg-gray-700"
              />
              <button className="px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white font-medium rounded-lg transition-colors">
                Notificar-me
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FutureImplementations;
