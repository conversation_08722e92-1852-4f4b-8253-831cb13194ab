"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jspdf-autotable";
exports.ids = ["vendor-chunks/jspdf-autotable"];
exports.modules = {

/***/ "(ssr)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Cell: () => (/* binding */ Cell),\n/* harmony export */   CellHookData: () => (/* binding */ CellHookData),\n/* harmony export */   Column: () => (/* binding */ Column),\n/* harmony export */   HookData: () => (/* binding */ HookData),\n/* harmony export */   Row: () => (/* binding */ Row),\n/* harmony export */   Table: () => (/* binding */ Table),\n/* harmony export */   __createTable: () => (/* binding */ __createTable),\n/* harmony export */   __drawTable: () => (/* binding */ __drawTable),\n/* harmony export */   applyPlugin: () => (/* binding */ applyPlugin),\n/* harmony export */   autoTable: () => (/* binding */ autoTable),\n/* harmony export */   \"default\": () => (/* binding */ autoTable)\n/* harmony export */ });\n/**\n * Improved text function with halign and valign support\n * Inspiration from: http://stackoverflow.com/questions/28327510/align-text-right-using-jspdf/28433113#28433113\n */\nfunction autoTableText (text, x, y, styles, doc) {\n    styles = styles || {};\n    var PHYSICAL_LINE_HEIGHT = 1.15;\n    var k = doc.internal.scaleFactor;\n    var fontSize = doc.internal.getFontSize() / k;\n    var lineHeightFactor = doc.getLineHeightFactor\n        ? doc.getLineHeightFactor()\n        : PHYSICAL_LINE_HEIGHT;\n    var lineHeight = fontSize * lineHeightFactor;\n    var splitRegex = /\\r\\n|\\r|\\n/g;\n    var splitText = '';\n    var lineCount = 1;\n    if (styles.valign === 'middle' ||\n        styles.valign === 'bottom' ||\n        styles.halign === 'center' ||\n        styles.halign === 'right') {\n        splitText = typeof text === 'string' ? text.split(splitRegex) : text;\n        lineCount = splitText.length || 1;\n    }\n    // Align the top\n    y += fontSize * (2 - PHYSICAL_LINE_HEIGHT);\n    if (styles.valign === 'middle')\n        y -= (lineCount / 2) * lineHeight;\n    else if (styles.valign === 'bottom')\n        y -= lineCount * lineHeight;\n    if (styles.halign === 'center' || styles.halign === 'right') {\n        var alignSize = fontSize;\n        if (styles.halign === 'center')\n            alignSize *= 0.5;\n        if (splitText && lineCount >= 1) {\n            for (var iLine = 0; iLine < splitText.length; iLine++) {\n                doc.text(splitText[iLine], x - doc.getStringUnitWidth(splitText[iLine]) * alignSize, y);\n                y += lineHeight;\n            }\n            return doc;\n        }\n        x -= doc.getStringUnitWidth(text) * alignSize;\n    }\n    if (styles.halign === 'justify') {\n        doc.text(text, x, y, { maxWidth: styles.maxWidth || 100, align: 'justify' });\n    }\n    else {\n        doc.text(text, x, y);\n    }\n    return doc;\n}\n\nvar globalDefaults = {};\nvar DocHandler = /** @class */ (function () {\n    function DocHandler(jsPDFDocument) {\n        this.jsPDFDocument = jsPDFDocument;\n        this.userStyles = {\n            // Black for versions of jspdf without getTextColor\n            textColor: jsPDFDocument.getTextColor\n                ? this.jsPDFDocument.getTextColor()\n                : 0,\n            fontSize: jsPDFDocument.internal.getFontSize(),\n            fontStyle: jsPDFDocument.internal.getFont().fontStyle,\n            font: jsPDFDocument.internal.getFont().fontName,\n            // 0 for versions of jspdf without getLineWidth\n            lineWidth: jsPDFDocument.getLineWidth\n                ? this.jsPDFDocument.getLineWidth()\n                : 0,\n            // Black for versions of jspdf without getDrawColor\n            lineColor: jsPDFDocument.getDrawColor\n                ? this.jsPDFDocument.getDrawColor()\n                : 0,\n        };\n    }\n    DocHandler.setDefaults = function (defaults, doc) {\n        if (doc === void 0) { doc = null; }\n        if (doc) {\n            doc.__autoTableDocumentDefaults = defaults;\n        }\n        else {\n            globalDefaults = defaults;\n        }\n    };\n    DocHandler.unifyColor = function (c) {\n        if (Array.isArray(c)) {\n            return c;\n        }\n        else if (typeof c === 'number') {\n            return [c, c, c];\n        }\n        else if (typeof c === 'string') {\n            return [c];\n        }\n        else {\n            return null;\n        }\n    };\n    DocHandler.prototype.applyStyles = function (styles, fontOnly) {\n        // Font style needs to be applied before font\n        // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/632\n        var _a, _b, _c;\n        if (fontOnly === void 0) { fontOnly = false; }\n        if (styles.fontStyle && this.jsPDFDocument.setFontStyle) {\n            this.jsPDFDocument.setFontStyle(styles.fontStyle);\n        }\n        var _d = this.jsPDFDocument.internal.getFont(), fontStyle = _d.fontStyle, fontName = _d.fontName;\n        if (styles.font)\n            fontName = styles.font;\n        if (styles.fontStyle) {\n            fontStyle = styles.fontStyle;\n            var availableFontStyles = this.getFontList()[fontName];\n            if (availableFontStyles &&\n                availableFontStyles.indexOf(fontStyle) === -1 &&\n                this.jsPDFDocument.setFontStyle) {\n                // Common issue was that the default bold in headers\n                // made custom fonts not work. For example:\n                // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/653\n                this.jsPDFDocument.setFontStyle(availableFontStyles[0]);\n                fontStyle = availableFontStyles[0];\n            }\n        }\n        this.jsPDFDocument.setFont(fontName, fontStyle);\n        if (styles.fontSize)\n            this.jsPDFDocument.setFontSize(styles.fontSize);\n        if (fontOnly) {\n            return; // Performance improvement\n        }\n        var color = DocHandler.unifyColor(styles.fillColor);\n        if (color)\n            (_a = this.jsPDFDocument).setFillColor.apply(_a, color);\n        color = DocHandler.unifyColor(styles.textColor);\n        if (color)\n            (_b = this.jsPDFDocument).setTextColor.apply(_b, color);\n        color = DocHandler.unifyColor(styles.lineColor);\n        if (color)\n            (_c = this.jsPDFDocument).setDrawColor.apply(_c, color);\n        if (typeof styles.lineWidth === 'number') {\n            this.jsPDFDocument.setLineWidth(styles.lineWidth);\n        }\n    };\n    DocHandler.prototype.splitTextToSize = function (text, size, opts) {\n        return this.jsPDFDocument.splitTextToSize(text, size, opts);\n    };\n    /**\n     * Adds a rectangle to the PDF\n     * @param x Coordinate (in units declared at inception of PDF document) against left edge of the page\n     * @param y Coordinate (in units declared at inception of PDF document) against upper edge of the page\n     * @param width Width (in units declared at inception of PDF document)\n     * @param height Height (in units declared at inception of PDF document)\n     * @param fillStyle A string specifying the painting style or null. Valid styles include: 'S' [default] - stroke, 'F' - fill, and 'DF' (or 'FD') - fill then stroke.\n     */\n    DocHandler.prototype.rect = function (x, y, width, height, fillStyle) {\n        // null is excluded from fillStyle possible values because it isn't needed\n        // and is prone to bugs as it's used to postpone setting the style\n        // https://rawgit.com/MrRio/jsPDF/master/docs/jsPDF.html#rect\n        return this.jsPDFDocument.rect(x, y, width, height, fillStyle);\n    };\n    DocHandler.prototype.getLastAutoTable = function () {\n        return this.jsPDFDocument.lastAutoTable || null;\n    };\n    DocHandler.prototype.getTextWidth = function (text) {\n        return this.jsPDFDocument.getTextWidth(text);\n    };\n    DocHandler.prototype.getDocument = function () {\n        return this.jsPDFDocument;\n    };\n    DocHandler.prototype.setPage = function (page) {\n        this.jsPDFDocument.setPage(page);\n    };\n    DocHandler.prototype.addPage = function () {\n        return this.jsPDFDocument.addPage();\n    };\n    DocHandler.prototype.getFontList = function () {\n        return this.jsPDFDocument.getFontList();\n    };\n    DocHandler.prototype.getGlobalOptions = function () {\n        return globalDefaults || {};\n    };\n    DocHandler.prototype.getDocumentOptions = function () {\n        return this.jsPDFDocument.__autoTableDocumentDefaults || {};\n    };\n    DocHandler.prototype.pageSize = function () {\n        var pageSize = this.jsPDFDocument.internal.pageSize;\n        // JSPDF 1.4 uses get functions instead of properties on pageSize\n        if (pageSize.width == null) {\n            pageSize = { width: pageSize.getWidth(), height: pageSize.getHeight() };\n        }\n        return pageSize;\n    };\n    DocHandler.prototype.scaleFactor = function () {\n        return this.jsPDFDocument.internal.scaleFactor;\n    };\n    DocHandler.prototype.getLineHeightFactor = function () {\n        var doc = this.jsPDFDocument;\n        return doc.getLineHeightFactor ? doc.getLineHeightFactor() : 1.15;\n    };\n    DocHandler.prototype.getLineHeight = function (fontSize) {\n        return (fontSize / this.scaleFactor()) * this.getLineHeightFactor();\n    };\n    DocHandler.prototype.pageNumber = function () {\n        var pageInfo = this.jsPDFDocument.internal.getCurrentPageInfo();\n        if (!pageInfo) {\n            // Only recent versions of jspdf has pageInfo\n            return this.jsPDFDocument.internal.getNumberOfPages();\n        }\n        return pageInfo.pageNumber;\n    };\n    return DocHandler;\n}());\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar HtmlRowInput = /** @class */ (function (_super) {\n    __extends(HtmlRowInput, _super);\n    function HtmlRowInput(element) {\n        var _this = _super.call(this) || this;\n        _this._element = element;\n        return _this;\n    }\n    return HtmlRowInput;\n}(Array));\n// Base style for all themes\nfunction defaultStyles(scaleFactor) {\n    return {\n        font: 'helvetica', // helvetica, times, courier\n        fontStyle: 'normal', // normal, bold, italic, bolditalic\n        overflow: 'linebreak', // linebreak, ellipsize, visible or hidden\n        fillColor: false, // Either false for transparent, rbg array e.g. [255, 255, 255] or gray level e.g 200\n        textColor: 20,\n        halign: 'left', // left, center, right, justify\n        valign: 'top', // top, middle, bottom\n        fontSize: 10,\n        cellPadding: 5 / scaleFactor, // number or {top,left,right,left,vertical,horizontal}\n        lineColor: 200,\n        lineWidth: 0,\n        cellWidth: 'auto', // 'auto'|'wrap'|number\n        minCellHeight: 0,\n        minCellWidth: 0,\n    };\n}\nfunction getTheme(name) {\n    var themes = {\n        striped: {\n            table: { fillColor: 255, textColor: 80, fontStyle: 'normal' },\n            head: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            body: {},\n            foot: { textColor: 255, fillColor: [41, 128, 185], fontStyle: 'bold' },\n            alternateRow: { fillColor: 245 },\n        },\n        grid: {\n            table: {\n                fillColor: 255,\n                textColor: 80,\n                fontStyle: 'normal',\n                lineWidth: 0.1,\n            },\n            head: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            body: {},\n            foot: {\n                textColor: 255,\n                fillColor: [26, 188, 156],\n                fontStyle: 'bold',\n                lineWidth: 0,\n            },\n            alternateRow: {},\n        },\n        plain: { head: { fontStyle: 'bold' }, foot: { fontStyle: 'bold' } },\n    };\n    return themes[name];\n}\n\nfunction getStringWidth(text, styles, doc) {\n    doc.applyStyles(styles, true);\n    var textArr = Array.isArray(text) ? text : [text];\n    var widestLineWidth = textArr\n        .map(function (text) { return doc.getTextWidth(text); })\n        .reduce(function (a, b) { return Math.max(a, b); }, 0);\n    return widestLineWidth;\n}\nfunction addTableBorder(doc, table, startPos, cursor) {\n    var lineWidth = table.settings.tableLineWidth;\n    var lineColor = table.settings.tableLineColor;\n    doc.applyStyles({ lineWidth: lineWidth, lineColor: lineColor });\n    var fillStyle = getFillStyle(lineWidth, false);\n    if (fillStyle) {\n        doc.rect(startPos.x, startPos.y, table.getWidth(doc.pageSize().width), cursor.y - startPos.y, fillStyle);\n    }\n}\nfunction getFillStyle(lineWidth, fillColor) {\n    var drawLine = lineWidth > 0;\n    var drawBackground = fillColor || fillColor === 0;\n    if (drawLine && drawBackground) {\n        return 'DF'; // Fill then stroke\n    }\n    else if (drawLine) {\n        return 'S'; // Only stroke (transparent background)\n    }\n    else if (drawBackground) {\n        return 'F'; // Only fill, no stroke\n    }\n    else {\n        return null;\n    }\n}\nfunction parseSpacing(value, defaultValue) {\n    var _a, _b, _c, _d;\n    value = value || defaultValue;\n    if (Array.isArray(value)) {\n        if (value.length >= 4) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[3],\n            };\n        }\n        else if (value.length === 3) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[2],\n                left: value[1],\n            };\n        }\n        else if (value.length === 2) {\n            return {\n                top: value[0],\n                right: value[1],\n                bottom: value[0],\n                left: value[1],\n            };\n        }\n        else if (value.length === 1) {\n            value = value[0];\n        }\n        else {\n            value = defaultValue;\n        }\n    }\n    if (typeof value === 'object') {\n        if (typeof value.vertical === 'number') {\n            value.top = value.vertical;\n            value.bottom = value.vertical;\n        }\n        if (typeof value.horizontal === 'number') {\n            value.right = value.horizontal;\n            value.left = value.horizontal;\n        }\n        return {\n            left: (_a = value.left) !== null && _a !== void 0 ? _a : defaultValue,\n            top: (_b = value.top) !== null && _b !== void 0 ? _b : defaultValue,\n            right: (_c = value.right) !== null && _c !== void 0 ? _c : defaultValue,\n            bottom: (_d = value.bottom) !== null && _d !== void 0 ? _d : defaultValue,\n        };\n    }\n    if (typeof value !== 'number') {\n        value = defaultValue;\n    }\n    return { top: value, right: value, bottom: value, left: value };\n}\nfunction getPageAvailableWidth(doc, table) {\n    var margins = parseSpacing(table.settings.margin, 0);\n    return doc.pageSize().width - (margins.left + margins.right);\n}\n\n// Limitations\n// - No support for border spacing\n// - No support for transparency\nfunction parseCss(supportedFonts, element, scaleFactor, style, window) {\n    var result = {};\n    var pxScaleFactor = 96 / 72;\n    var backgroundColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['backgroundColor'];\n    });\n    if (backgroundColor != null)\n        result.fillColor = backgroundColor;\n    var textColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)['color'];\n    });\n    if (textColor != null)\n        result.textColor = textColor;\n    var padding = parsePadding(style, scaleFactor);\n    if (padding)\n        result.cellPadding = padding;\n    var borderColorSide = 'borderTopColor';\n    var finalScaleFactor = pxScaleFactor * scaleFactor;\n    var btw = style.borderTopWidth;\n    if (style.borderBottomWidth === btw &&\n        style.borderRightWidth === btw &&\n        style.borderLeftWidth === btw) {\n        var borderWidth = (parseFloat(btw) || 0) / finalScaleFactor;\n        if (borderWidth)\n            result.lineWidth = borderWidth;\n    }\n    else {\n        result.lineWidth = {\n            top: (parseFloat(style.borderTopWidth) || 0) / finalScaleFactor,\n            right: (parseFloat(style.borderRightWidth) || 0) / finalScaleFactor,\n            bottom: (parseFloat(style.borderBottomWidth) || 0) / finalScaleFactor,\n            left: (parseFloat(style.borderLeftWidth) || 0) / finalScaleFactor,\n        };\n        // Choose border color of first available side\n        // could be improved by supporting object as lineColor\n        if (!result.lineWidth.top) {\n            if (result.lineWidth.right) {\n                borderColorSide = 'borderRightColor';\n            }\n            else if (result.lineWidth.bottom) {\n                borderColorSide = 'borderBottomColor';\n            }\n            else if (result.lineWidth.left) {\n                borderColorSide = 'borderLeftColor';\n            }\n        }\n    }\n    var borderColor = parseColor(element, function (elem) {\n        return window.getComputedStyle(elem)[borderColorSide];\n    });\n    if (borderColor != null)\n        result.lineColor = borderColor;\n    var accepted = ['left', 'right', 'center', 'justify'];\n    if (accepted.indexOf(style.textAlign) !== -1) {\n        result.halign = style.textAlign;\n    }\n    accepted = ['middle', 'bottom', 'top'];\n    if (accepted.indexOf(style.verticalAlign) !== -1) {\n        result.valign = style.verticalAlign;\n    }\n    var res = parseInt(style.fontSize || '');\n    if (!isNaN(res))\n        result.fontSize = res / pxScaleFactor;\n    var fontStyle = parseFontStyle(style);\n    if (fontStyle)\n        result.fontStyle = fontStyle;\n    var font = (style.fontFamily || '').toLowerCase();\n    if (supportedFonts.indexOf(font) !== -1) {\n        result.font = font;\n    }\n    return result;\n}\nfunction parseFontStyle(style) {\n    var res = '';\n    if (style.fontWeight === 'bold' ||\n        style.fontWeight === 'bolder' ||\n        parseInt(style.fontWeight) >= 700) {\n        res = 'bold';\n    }\n    if (style.fontStyle === 'italic' || style.fontStyle === 'oblique') {\n        res += 'italic';\n    }\n    return res;\n}\nfunction parseColor(element, styleGetter) {\n    var cssColor = realColor(element, styleGetter);\n    if (!cssColor)\n        return null;\n    var rgba = cssColor.match(/^rgba?\\((\\d+),\\s*(\\d+),\\s*(\\d+)(?:,\\s*(\\d*\\.?\\d*))?\\)$/);\n    if (!rgba || !Array.isArray(rgba)) {\n        return null;\n    }\n    var color = [\n        parseInt(rgba[1]),\n        parseInt(rgba[2]),\n        parseInt(rgba[3]),\n    ];\n    var alpha = parseInt(rgba[4]);\n    if (alpha === 0 || isNaN(color[0]) || isNaN(color[1]) || isNaN(color[2])) {\n        return null;\n    }\n    return color;\n}\nfunction realColor(elem, styleGetter) {\n    var bg = styleGetter(elem);\n    if (bg === 'rgba(0, 0, 0, 0)' ||\n        bg === 'transparent' ||\n        bg === 'initial' ||\n        bg === 'inherit') {\n        if (elem.parentElement == null) {\n            return null;\n        }\n        return realColor(elem.parentElement, styleGetter);\n    }\n    else {\n        return bg;\n    }\n}\nfunction parsePadding(style, scaleFactor) {\n    var val = [\n        style.paddingTop,\n        style.paddingRight,\n        style.paddingBottom,\n        style.paddingLeft,\n    ];\n    var pxScaleFactor = 96 / (72 / scaleFactor);\n    var linePadding = (parseInt(style.lineHeight) - parseInt(style.fontSize)) / scaleFactor / 2;\n    var inputPadding = val.map(function (n) {\n        return parseInt(n || '0') / pxScaleFactor;\n    });\n    var padding = parseSpacing(inputPadding, 0);\n    if (linePadding > padding.top) {\n        padding.top = linePadding;\n    }\n    if (linePadding > padding.bottom) {\n        padding.bottom = linePadding;\n    }\n    return padding;\n}\n\nfunction parseHtml(doc, input, window, includeHiddenHtml, useCss) {\n    var _a, _b;\n    if (includeHiddenHtml === void 0) { includeHiddenHtml = false; }\n    if (useCss === void 0) { useCss = false; }\n    var tableElement;\n    if (typeof input === 'string') {\n        tableElement = window.document.querySelector(input);\n    }\n    else {\n        tableElement = input;\n    }\n    var supportedFonts = Object.keys(doc.getFontList());\n    var scaleFactor = doc.scaleFactor();\n    var head = [], body = [], foot = [];\n    if (!tableElement) {\n        console.error('Html table could not be found with input: ', input);\n        return { head: head, body: body, foot: foot };\n    }\n    for (var i = 0; i < tableElement.rows.length; i++) {\n        var element = tableElement.rows[i];\n        var tagName = (_b = (_a = element === null || element === void 0 ? void 0 : element.parentElement) === null || _a === void 0 ? void 0 : _a.tagName) === null || _b === void 0 ? void 0 : _b.toLowerCase();\n        var row = parseRowContent(supportedFonts, scaleFactor, window, element, includeHiddenHtml, useCss);\n        if (!row)\n            continue;\n        if (tagName === 'thead') {\n            head.push(row);\n        }\n        else if (tagName === 'tfoot') {\n            foot.push(row);\n        }\n        else {\n            // Add to body both if parent is tbody or table\n            body.push(row);\n        }\n    }\n    return { head: head, body: body, foot: foot };\n}\nfunction parseRowContent(supportedFonts, scaleFactor, window, row, includeHidden, useCss) {\n    var resultRow = new HtmlRowInput(row);\n    for (var i = 0; i < row.cells.length; i++) {\n        var cell = row.cells[i];\n        var style_1 = window.getComputedStyle(cell);\n        if (includeHidden || style_1.display !== 'none') {\n            var cellStyles = void 0;\n            if (useCss) {\n                cellStyles = parseCss(supportedFonts, cell, scaleFactor, style_1, window);\n            }\n            resultRow.push({\n                rowSpan: cell.rowSpan,\n                colSpan: cell.colSpan,\n                styles: cellStyles,\n                _element: cell,\n                content: parseCellContent(cell),\n            });\n        }\n    }\n    var style = window.getComputedStyle(row);\n    if (resultRow.length > 0 && (includeHidden || style.display !== 'none')) {\n        return resultRow;\n    }\n}\nfunction parseCellContent(orgCell) {\n    // Work on cloned node to make sure no changes are applied to html table\n    var cell = orgCell.cloneNode(true);\n    // Remove extra space and line breaks in markup to make it more similar to\n    // what would be shown in html\n    cell.innerHTML = cell.innerHTML.replace(/\\n/g, '').replace(/ +/g, ' ');\n    // Preserve <br> tags as line breaks in the pdf\n    cell.innerHTML = cell.innerHTML\n        .split(/<br.*?>/) //start with '<br' and ends with '>'.\n        .map(function (part) { return part.trim(); })\n        .join('\\n');\n    // innerText for ie\n    return cell.innerText || cell.textContent || '';\n}\n\nfunction validateInput(global, document, current) {\n    for (var _i = 0, _a = [global, document, current]; _i < _a.length; _i++) {\n        var options = _a[_i];\n        if (options && typeof options !== 'object') {\n            console.error('The options parameter should be of type object, is: ' + typeof options);\n        }\n        if (options.startY && typeof options.startY !== 'number') {\n            console.error('Invalid value for startY option', options.startY);\n            delete options.startY;\n        }\n    }\n}\n\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\nfunction assign(target, s, s1, s2, s3) {\n    if (target == null) {\n        throw new TypeError('Cannot convert undefined or null to object');\n    }\n    var to = Object(target);\n    for (var index = 1; index < arguments.length; index++) {\n        // eslint-disable-next-line prefer-rest-params\n        var nextSource = arguments[index];\n        if (nextSource != null) {\n            // Skip over if undefined or null\n            for (var nextKey in nextSource) {\n                // Avoid bugs when hasOwnProperty is shadowed\n                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {\n                    to[nextKey] = nextSource[nextKey];\n                }\n            }\n        }\n    }\n    return to;\n}\n\nfunction parseInput(d, current) {\n    var doc = new DocHandler(d);\n    var document = doc.getDocumentOptions();\n    var global = doc.getGlobalOptions();\n    validateInput(global, document, current);\n    var options = assign({}, global, document, current);\n    var win;\n    if (typeof window !== 'undefined') {\n        win = window;\n    }\n    var styles = parseStyles(global, document, current);\n    var hooks = parseHooks(global, document, current);\n    var settings = parseSettings(doc, options);\n    var content = parseContent$1(doc, options, win);\n    return { id: current.tableId, content: content, hooks: hooks, styles: styles, settings: settings };\n}\nfunction parseStyles(gInput, dInput, cInput) {\n    var styleOptions = {\n        styles: {},\n        headStyles: {},\n        bodyStyles: {},\n        footStyles: {},\n        alternateRowStyles: {},\n        columnStyles: {},\n    };\n    var _loop_1 = function (prop) {\n        if (prop === 'columnStyles') {\n            var global_1 = gInput[prop];\n            var document_1 = dInput[prop];\n            var current = cInput[prop];\n            styleOptions.columnStyles = assign({}, global_1, document_1, current);\n        }\n        else {\n            var allOptions = [gInput, dInput, cInput];\n            var styles = allOptions.map(function (opts) { return opts[prop] || {}; });\n            styleOptions[prop] = assign({}, styles[0], styles[1], styles[2]);\n        }\n    };\n    for (var _i = 0, _a = Object.keys(styleOptions); _i < _a.length; _i++) {\n        var prop = _a[_i];\n        _loop_1(prop);\n    }\n    return styleOptions;\n}\nfunction parseHooks(global, document, current) {\n    var allOptions = [global, document, current];\n    var result = {\n        didParseCell: [],\n        willDrawCell: [],\n        didDrawCell: [],\n        willDrawPage: [],\n        didDrawPage: [],\n    };\n    for (var _i = 0, allOptions_1 = allOptions; _i < allOptions_1.length; _i++) {\n        var options = allOptions_1[_i];\n        if (options.didParseCell)\n            result.didParseCell.push(options.didParseCell);\n        if (options.willDrawCell)\n            result.willDrawCell.push(options.willDrawCell);\n        if (options.didDrawCell)\n            result.didDrawCell.push(options.didDrawCell);\n        if (options.willDrawPage)\n            result.willDrawPage.push(options.willDrawPage);\n        if (options.didDrawPage)\n            result.didDrawPage.push(options.didDrawPage);\n    }\n    return result;\n}\nfunction parseSettings(doc, options) {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;\n    var margin = parseSpacing(options.margin, 40 / doc.scaleFactor());\n    var startY = (_a = getStartY(doc, options.startY)) !== null && _a !== void 0 ? _a : margin.top;\n    var showFoot;\n    if (options.showFoot === true) {\n        showFoot = 'everyPage';\n    }\n    else if (options.showFoot === false) {\n        showFoot = 'never';\n    }\n    else {\n        showFoot = (_b = options.showFoot) !== null && _b !== void 0 ? _b : 'everyPage';\n    }\n    var showHead;\n    if (options.showHead === true) {\n        showHead = 'everyPage';\n    }\n    else if (options.showHead === false) {\n        showHead = 'never';\n    }\n    else {\n        showHead = (_c = options.showHead) !== null && _c !== void 0 ? _c : 'everyPage';\n    }\n    var useCss = (_d = options.useCss) !== null && _d !== void 0 ? _d : false;\n    var theme = options.theme || (useCss ? 'plain' : 'striped');\n    var horizontalPageBreak = !!options.horizontalPageBreak;\n    var horizontalPageBreakRepeat = (_e = options.horizontalPageBreakRepeat) !== null && _e !== void 0 ? _e : null;\n    return {\n        includeHiddenHtml: (_f = options.includeHiddenHtml) !== null && _f !== void 0 ? _f : false,\n        useCss: useCss,\n        theme: theme,\n        startY: startY,\n        margin: margin,\n        pageBreak: (_g = options.pageBreak) !== null && _g !== void 0 ? _g : 'auto',\n        rowPageBreak: (_h = options.rowPageBreak) !== null && _h !== void 0 ? _h : 'auto',\n        tableWidth: (_j = options.tableWidth) !== null && _j !== void 0 ? _j : 'auto',\n        showHead: showHead,\n        showFoot: showFoot,\n        tableLineWidth: (_k = options.tableLineWidth) !== null && _k !== void 0 ? _k : 0,\n        tableLineColor: (_l = options.tableLineColor) !== null && _l !== void 0 ? _l : 200,\n        horizontalPageBreak: horizontalPageBreak,\n        horizontalPageBreakRepeat: horizontalPageBreakRepeat,\n        horizontalPageBreakBehaviour: (_m = options.horizontalPageBreakBehaviour) !== null && _m !== void 0 ? _m : 'afterAllRows',\n    };\n}\nfunction getStartY(doc, userStartY) {\n    var previous = doc.getLastAutoTable();\n    var sf = doc.scaleFactor();\n    var currentPage = doc.pageNumber();\n    var isSamePageAsPreviousTable = false;\n    if (previous && previous.startPageNumber) {\n        var endingPage = previous.startPageNumber + previous.pageNumber - 1;\n        isSamePageAsPreviousTable = endingPage === currentPage;\n    }\n    if (typeof userStartY === 'number') {\n        return userStartY;\n    }\n    else if (userStartY == null || userStartY === false) {\n        if (isSamePageAsPreviousTable && (previous === null || previous === void 0 ? void 0 : previous.finalY) != null) {\n            // Some users had issues with overlapping tables when they used multiple\n            // tables without setting startY so setting it here to a sensible default.\n            return previous.finalY + 20 / sf;\n        }\n    }\n    return null;\n}\nfunction parseContent$1(doc, options, window) {\n    var head = options.head || [];\n    var body = options.body || [];\n    var foot = options.foot || [];\n    if (options.html) {\n        var hidden = options.includeHiddenHtml;\n        if (window) {\n            var htmlContent = parseHtml(doc, options.html, window, hidden, options.useCss) || {};\n            head = htmlContent.head || head;\n            body = htmlContent.body || head;\n            foot = htmlContent.foot || head;\n        }\n        else {\n            console.error('Cannot parse html in non browser environment');\n        }\n    }\n    var columns = options.columns || parseColumns(head, body, foot);\n    return { columns: columns, head: head, body: body, foot: foot };\n}\nfunction parseColumns(head, body, foot) {\n    var firstRow = head[0] || body[0] || foot[0] || [];\n    var result = [];\n    Object.keys(firstRow)\n        .filter(function (key) { return key !== '_element'; })\n        .forEach(function (key) {\n        var colSpan = 1;\n        var input;\n        if (Array.isArray(firstRow)) {\n            input = firstRow[parseInt(key)];\n        }\n        else {\n            input = firstRow[key];\n        }\n        if (typeof input === 'object' && !Array.isArray(input)) {\n            colSpan = (input === null || input === void 0 ? void 0 : input.colSpan) || 1;\n        }\n        for (var i = 0; i < colSpan; i++) {\n            var id = void 0;\n            if (Array.isArray(firstRow)) {\n                id = result.length;\n            }\n            else {\n                id = key + (i > 0 ? \"_\".concat(i) : '');\n            }\n            var rowResult = { dataKey: id };\n            result.push(rowResult);\n        }\n    });\n    return result;\n}\n\nvar HookData = /** @class */ (function () {\n    function HookData(doc, table, cursor) {\n        this.table = table;\n        this.pageNumber = table.pageNumber;\n        this.settings = table.settings;\n        this.cursor = cursor;\n        this.doc = doc.getDocument();\n    }\n    return HookData;\n}());\nvar CellHookData = /** @class */ (function (_super) {\n    __extends(CellHookData, _super);\n    function CellHookData(doc, table, cell, row, column, cursor) {\n        var _this = _super.call(this, doc, table, cursor) || this;\n        _this.cell = cell;\n        _this.row = row;\n        _this.column = column;\n        _this.section = row.section;\n        return _this;\n    }\n    return CellHookData;\n}(HookData));\n\nvar Table = /** @class */ (function () {\n    function Table(input, content) {\n        this.pageNumber = 1;\n        this.id = input.id;\n        this.settings = input.settings;\n        this.styles = input.styles;\n        this.hooks = input.hooks;\n        this.columns = content.columns;\n        this.head = content.head;\n        this.body = content.body;\n        this.foot = content.foot;\n    }\n    Table.prototype.getHeadHeight = function (columns) {\n        return this.head.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.getFootHeight = function (columns) {\n        return this.foot.reduce(function (acc, row) { return acc + row.getMaxCellHeight(columns); }, 0);\n    };\n    Table.prototype.allRows = function () {\n        return this.head.concat(this.body).concat(this.foot);\n    };\n    Table.prototype.callCellHooks = function (doc, handlers, cell, row, column, cursor) {\n        for (var _i = 0, handlers_1 = handlers; _i < handlers_1.length; _i++) {\n            var handler = handlers_1[_i];\n            var data = new CellHookData(doc, this, cell, row, column, cursor);\n            var result = handler(data) === false;\n            // Make sure text is always string[] since user can assign string\n            cell.text = Array.isArray(cell.text) ? cell.text : [cell.text];\n            if (result) {\n                return false;\n            }\n        }\n        return true;\n    };\n    Table.prototype.callEndPageHooks = function (doc, cursor) {\n        doc.applyStyles(doc.userStyles);\n        for (var _i = 0, _a = this.hooks.didDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.callWillDrawPageHooks = function (doc, cursor) {\n        for (var _i = 0, _a = this.hooks.willDrawPage; _i < _a.length; _i++) {\n            var handler = _a[_i];\n            handler(new HookData(doc, this, cursor));\n        }\n    };\n    Table.prototype.getWidth = function (pageWidth) {\n        if (typeof this.settings.tableWidth === 'number') {\n            return this.settings.tableWidth;\n        }\n        else if (this.settings.tableWidth === 'wrap') {\n            var wrappedWidth = this.columns.reduce(function (total, col) { return total + col.wrappedWidth; }, 0);\n            return wrappedWidth;\n        }\n        else {\n            var margin = this.settings.margin;\n            return pageWidth - margin.left - margin.right;\n        }\n    };\n    return Table;\n}());\nvar Row = /** @class */ (function () {\n    function Row(raw, index, section, cells, spansMultiplePages) {\n        if (spansMultiplePages === void 0) { spansMultiplePages = false; }\n        this.height = 0;\n        this.raw = raw;\n        if (raw instanceof HtmlRowInput) {\n            this.raw = raw._element;\n            this.element = raw._element;\n        }\n        this.index = index;\n        this.section = section;\n        this.cells = cells;\n        this.spansMultiplePages = spansMultiplePages;\n    }\n    Row.prototype.getMaxCellHeight = function (columns) {\n        var _this = this;\n        return columns.reduce(function (acc, column) { var _a; return Math.max(acc, ((_a = _this.cells[column.index]) === null || _a === void 0 ? void 0 : _a.height) || 0); }, 0);\n    };\n    Row.prototype.hasRowSpan = function (columns) {\n        var _this = this;\n        return (columns.filter(function (column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return false;\n            return cell.rowSpan > 1;\n        }).length > 0);\n    };\n    Row.prototype.canEntireRowFit = function (height, columns) {\n        return this.getMaxCellHeight(columns) <= height;\n    };\n    Row.prototype.getMinimumRowHeight = function (columns, doc) {\n        var _this = this;\n        return columns.reduce(function (acc, column) {\n            var cell = _this.cells[column.index];\n            if (!cell)\n                return 0;\n            var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n            var vPadding = cell.padding('vertical');\n            var oneRowHeight = vPadding + lineHeight;\n            return oneRowHeight > acc ? oneRowHeight : acc;\n        }, 0);\n    };\n    return Row;\n}());\nvar Cell = /** @class */ (function () {\n    function Cell(raw, styles, section) {\n        var _a;\n        this.contentHeight = 0;\n        this.contentWidth = 0;\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.height = 0;\n        this.x = 0;\n        this.y = 0;\n        this.styles = styles;\n        this.section = section;\n        this.raw = raw;\n        var content = raw;\n        if (raw != null && typeof raw === 'object' && !Array.isArray(raw)) {\n            this.rowSpan = raw.rowSpan || 1;\n            this.colSpan = raw.colSpan || 1;\n            content = (_a = raw.content) !== null && _a !== void 0 ? _a : raw;\n            if (raw._element) {\n                this.raw = raw._element;\n            }\n        }\n        else {\n            this.rowSpan = 1;\n            this.colSpan = 1;\n        }\n        // Stringify 0 and false, but not undefined or null\n        var text = content != null ? '' + content : '';\n        var splitRegex = /\\r\\n|\\r|\\n/g;\n        this.text = text.split(splitRegex);\n    }\n    Cell.prototype.getTextPos = function () {\n        var y;\n        if (this.styles.valign === 'top') {\n            y = this.y + this.padding('top');\n        }\n        else if (this.styles.valign === 'bottom') {\n            y = this.y + this.height - this.padding('bottom');\n        }\n        else {\n            var netHeight = this.height - this.padding('vertical');\n            y = this.y + netHeight / 2 + this.padding('top');\n        }\n        var x;\n        if (this.styles.halign === 'right') {\n            x = this.x + this.width - this.padding('right');\n        }\n        else if (this.styles.halign === 'center') {\n            var netWidth = this.width - this.padding('horizontal');\n            x = this.x + netWidth / 2 + this.padding('left');\n        }\n        else {\n            x = this.x + this.padding('left');\n        }\n        return { x: x, y: y };\n    };\n    // TODO (v4): replace parameters with only (lineHeight)\n    Cell.prototype.getContentHeight = function (scaleFactor, lineHeightFactor) {\n        if (lineHeightFactor === void 0) { lineHeightFactor = 1.15; }\n        var lineCount = Array.isArray(this.text) ? this.text.length : 1;\n        var lineHeight = (this.styles.fontSize / scaleFactor) * lineHeightFactor;\n        var height = lineCount * lineHeight + this.padding('vertical');\n        return Math.max(height, this.styles.minCellHeight);\n    };\n    Cell.prototype.padding = function (name) {\n        var padding = parseSpacing(this.styles.cellPadding, 0);\n        if (name === 'vertical') {\n            return padding.top + padding.bottom;\n        }\n        else if (name === 'horizontal') {\n            return padding.left + padding.right;\n        }\n        else {\n            return padding[name];\n        }\n    };\n    return Cell;\n}());\nvar Column = /** @class */ (function () {\n    function Column(dataKey, raw, index) {\n        this.wrappedWidth = 0;\n        this.minReadableWidth = 0;\n        this.minWidth = 0;\n        this.width = 0;\n        this.dataKey = dataKey;\n        this.raw = raw;\n        this.index = index;\n    }\n    Column.prototype.getMaxCustomCellWidth = function (table) {\n        var max = 0;\n        for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n            var row = _a[_i];\n            var cell = row.cells[this.index];\n            if (cell && typeof cell.styles.cellWidth === 'number') {\n                max = Math.max(max, cell.styles.cellWidth);\n            }\n        }\n        return max;\n    };\n    return Column;\n}());\n\n/**\n * Calculate the column widths\n */\nfunction calculateWidths(doc, table) {\n    calculate(doc, table);\n    var resizableColumns = [];\n    var initialTableWidth = 0;\n    table.columns.forEach(function (column) {\n        var customWidth = column.getMaxCustomCellWidth(table);\n        if (customWidth) {\n            // final column width\n            column.width = customWidth;\n        }\n        else {\n            // initial column width (will be resized)\n            column.width = column.wrappedWidth;\n            resizableColumns.push(column);\n        }\n        initialTableWidth += column.width;\n    });\n    // width difference that needs to be distributed\n    var resizeWidth = table.getWidth(doc.pageSize().width) - initialTableWidth;\n    // first resize attempt: with respect to minReadableWidth and minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) {\n            return Math.max(column.minReadableWidth, column.minWidth);\n        });\n    }\n    // second resize attempt: ignore minReadableWidth but respect minWidth\n    if (resizeWidth) {\n        resizeWidth = resizeColumns(resizableColumns, resizeWidth, function (column) { return column.minWidth; });\n    }\n    resizeWidth = Math.abs(resizeWidth);\n    if (!table.settings.horizontalPageBreak &&\n        resizeWidth > 0.1 / doc.scaleFactor()) {\n        // Table can't get smaller due to custom-width or minWidth restrictions\n        // We can't really do much here. Up to user to for example\n        // reduce font size, increase page size or remove custom cell widths\n        // to allow more columns to be reduced in size\n        resizeWidth = resizeWidth < 1 ? resizeWidth : Math.round(resizeWidth);\n        console.warn(\"Of the table content, \".concat(resizeWidth, \" units width could not fit page\"));\n    }\n    applyColSpans(table);\n    fitContent(table, doc);\n    applyRowSpans(table);\n}\nfunction calculate(doc, table) {\n    var sf = doc.scaleFactor();\n    var horizontalPageBreak = table.settings.horizontalPageBreak;\n    var availablePageWidth = getPageAvailableWidth(doc, table);\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            var hooks = table.hooks.didParseCell;\n            table.callCellHooks(doc, hooks, cell, row, column, null);\n            var padding = cell.padding('horizontal');\n            cell.contentWidth = getStringWidth(cell.text, cell.styles, doc) + padding;\n            // Using [^\\S\\u00A0] instead of \\s ensures that we split the text on all\n            // whitespace except non-breaking spaces (\\u00A0). We need to preserve\n            // them in the split process to ensure correct word separation and width\n            // calculation.\n            var longestWordWidth = getStringWidth(cell.text.join(' ').split(/[^\\S\\u00A0]+/), cell.styles, doc);\n            cell.minReadableWidth = longestWordWidth + cell.padding('horizontal');\n            if (typeof cell.styles.cellWidth === 'number') {\n                cell.minWidth = cell.styles.cellWidth;\n                cell.wrappedWidth = cell.styles.cellWidth;\n            }\n            else if (cell.styles.cellWidth === 'wrap' ||\n                horizontalPageBreak === true) {\n                // cell width should not be more than available page width\n                if (cell.contentWidth > availablePageWidth) {\n                    cell.minWidth = availablePageWidth;\n                    cell.wrappedWidth = availablePageWidth;\n                }\n                else {\n                    cell.minWidth = cell.contentWidth;\n                    cell.wrappedWidth = cell.contentWidth;\n                }\n            }\n            else {\n                // auto\n                var defaultMinWidth = 10 / sf;\n                cell.minWidth = cell.styles.minCellWidth || defaultMinWidth;\n                cell.wrappedWidth = cell.contentWidth;\n                if (cell.minWidth > cell.wrappedWidth) {\n                    cell.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n    table.allRows().forEach(function (row) {\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var cell = row.cells[column.index];\n            // For now we ignore the minWidth and wrappedWidth of colspan cells when calculating colspan widths.\n            // Could probably be improved upon however.\n            if (cell && cell.colSpan === 1) {\n                column.wrappedWidth = Math.max(column.wrappedWidth, cell.wrappedWidth);\n                column.minWidth = Math.max(column.minWidth, cell.minWidth);\n                column.minReadableWidth = Math.max(column.minReadableWidth, cell.minReadableWidth);\n            }\n            else {\n                // Respect cellWidth set in columnStyles even if there is no cells for this column\n                // or if the column only have colspan cells. Since the width of colspan cells\n                // does not affect the width of columns, setting columnStyles cellWidth enables the\n                // user to at least do it manually.\n                // Note that this is not perfect for now since for example row and table styles are\n                // not accounted for\n                var columnStyles = table.styles.columnStyles[column.dataKey] ||\n                    table.styles.columnStyles[column.index] ||\n                    {};\n                var cellWidth = columnStyles.cellWidth || columnStyles.minCellWidth;\n                if (cellWidth && typeof cellWidth === 'number') {\n                    column.minWidth = cellWidth;\n                    column.wrappedWidth = cellWidth;\n                }\n            }\n            if (cell) {\n                // Make sure all columns get at least min width even though width calculations are not based on them\n                if (cell.colSpan > 1 && !column.minWidth) {\n                    column.minWidth = cell.minWidth;\n                }\n                if (cell.colSpan > 1 && !column.wrappedWidth) {\n                    column.wrappedWidth = cell.minWidth;\n                }\n            }\n        }\n    });\n}\n/**\n * Distribute resizeWidth on passed resizable columns\n */\nfunction resizeColumns(columns, resizeWidth, getMinWidth) {\n    var initialResizeWidth = resizeWidth;\n    var sumWrappedWidth = columns.reduce(function (acc, column) { return acc + column.wrappedWidth; }, 0);\n    for (var i = 0; i < columns.length; i++) {\n        var column = columns[i];\n        var ratio = column.wrappedWidth / sumWrappedWidth;\n        var suggestedChange = initialResizeWidth * ratio;\n        var suggestedWidth = column.width + suggestedChange;\n        var minWidth = getMinWidth(column);\n        var newWidth = suggestedWidth < minWidth ? minWidth : suggestedWidth;\n        resizeWidth -= newWidth - column.width;\n        column.width = newWidth;\n    }\n    resizeWidth = Math.round(resizeWidth * 1e10) / 1e10;\n    // Run the resizer again if there's remaining width needs\n    // to be distributed and there're columns that can be resized\n    if (resizeWidth) {\n        var resizableColumns = columns.filter(function (column) {\n            return resizeWidth < 0\n                ? column.width > getMinWidth(column) // check if column can shrink\n                : true; // check if column can grow\n        });\n        if (resizableColumns.length) {\n            resizeWidth = resizeColumns(resizableColumns, resizeWidth, getMinWidth);\n        }\n    }\n    return resizeWidth;\n}\nfunction applyRowSpans(table) {\n    var rowSpanCells = {};\n    var colRowSpansLeft = 1;\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n            var column = _a[_i];\n            var data = rowSpanCells[column.index];\n            if (colRowSpansLeft > 1) {\n                colRowSpansLeft--;\n                delete row.cells[column.index];\n            }\n            else if (data) {\n                data.cell.height += row.height;\n                colRowSpansLeft = data.cell.colSpan;\n                delete row.cells[column.index];\n                data.left--;\n                if (data.left <= 1) {\n                    delete rowSpanCells[column.index];\n                }\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell) {\n                    continue;\n                }\n                cell.height = row.height;\n                if (cell.rowSpan > 1) {\n                    var remaining = all.length - rowIndex;\n                    var left = cell.rowSpan > remaining ? remaining : cell.rowSpan;\n                    rowSpanCells[column.index] = { cell: cell, left: left, row: row };\n                }\n            }\n        }\n    }\n}\nfunction applyColSpans(table) {\n    var all = table.allRows();\n    for (var rowIndex = 0; rowIndex < all.length; rowIndex++) {\n        var row = all[rowIndex];\n        var colSpanCell = null;\n        var combinedColSpanWidth = 0;\n        var colSpansLeft = 0;\n        for (var columnIndex = 0; columnIndex < table.columns.length; columnIndex++) {\n            var column = table.columns[columnIndex];\n            // Width and colspan\n            colSpansLeft -= 1;\n            if (colSpansLeft > 1 && table.columns[columnIndex + 1]) {\n                combinedColSpanWidth += column.width;\n                delete row.cells[column.index];\n            }\n            else if (colSpanCell) {\n                var cell = colSpanCell;\n                delete row.cells[column.index];\n                colSpanCell = null;\n                cell.width = column.width + combinedColSpanWidth;\n            }\n            else {\n                var cell = row.cells[column.index];\n                if (!cell)\n                    continue;\n                colSpansLeft = cell.colSpan;\n                combinedColSpanWidth = 0;\n                if (cell.colSpan > 1) {\n                    colSpanCell = cell;\n                    combinedColSpanWidth += column.width;\n                    continue;\n                }\n                cell.width = column.width + combinedColSpanWidth;\n            }\n        }\n    }\n}\nfunction fitContent(table, doc) {\n    var rowSpanHeight = { count: 0, height: 0 };\n    for (var _i = 0, _a = table.allRows(); _i < _a.length; _i++) {\n        var row = _a[_i];\n        for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n            var column = _c[_b];\n            var cell = row.cells[column.index];\n            if (!cell)\n                continue;\n            doc.applyStyles(cell.styles, true);\n            var textSpace = cell.width - cell.padding('horizontal');\n            if (cell.styles.overflow === 'linebreak') {\n                // Add one pt to textSpace to fix rounding error\n                cell.text = doc.splitTextToSize(cell.text, textSpace + 1 / doc.scaleFactor(), { fontSize: cell.styles.fontSize });\n            }\n            else if (cell.styles.overflow === 'ellipsize') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '...');\n            }\n            else if (cell.styles.overflow === 'hidden') {\n                cell.text = ellipsize(cell.text, textSpace, cell.styles, doc, '');\n            }\n            else if (typeof cell.styles.overflow === 'function') {\n                var result = cell.styles.overflow(cell.text, textSpace);\n                if (typeof result === 'string') {\n                    cell.text = [result];\n                }\n                else {\n                    cell.text = result;\n                }\n            }\n            cell.contentHeight = cell.getContentHeight(doc.scaleFactor(), doc.getLineHeightFactor());\n            var realContentHeight = cell.contentHeight / cell.rowSpan;\n            if (cell.rowSpan > 1 &&\n                rowSpanHeight.count * rowSpanHeight.height <\n                    realContentHeight * cell.rowSpan) {\n                rowSpanHeight = { height: realContentHeight, count: cell.rowSpan };\n            }\n            else if (rowSpanHeight && rowSpanHeight.count > 0) {\n                if (rowSpanHeight.height > realContentHeight) {\n                    realContentHeight = rowSpanHeight.height;\n                }\n            }\n            if (realContentHeight > row.height) {\n                row.height = realContentHeight;\n            }\n        }\n        rowSpanHeight.count--;\n    }\n}\nfunction ellipsize(text, width, styles, doc, overflow) {\n    return text.map(function (str) { return ellipsizeStr(str, width, styles, doc, overflow); });\n}\nfunction ellipsizeStr(text, width, styles, doc, overflow) {\n    var precision = 10000 * doc.scaleFactor();\n    width = Math.ceil(width * precision) / precision;\n    if (width >= getStringWidth(text, styles, doc)) {\n        return text;\n    }\n    while (width < getStringWidth(text + overflow, styles, doc)) {\n        if (text.length <= 1) {\n            break;\n        }\n        text = text.substring(0, text.length - 1);\n    }\n    return text.trim() + overflow;\n}\n\nfunction createTable(jsPDFDoc, input) {\n    var doc = new DocHandler(jsPDFDoc);\n    var content = parseContent(input, doc.scaleFactor());\n    var table = new Table(input, content);\n    calculateWidths(doc, table);\n    doc.applyStyles(doc.userStyles);\n    return table;\n}\nfunction parseContent(input, sf) {\n    var content = input.content;\n    var columns = createColumns(content.columns);\n    // If no head or foot is set, try generating it with content from columns\n    if (content.head.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'head');\n        if (sectionRow)\n            content.head.push(sectionRow);\n    }\n    if (content.foot.length === 0) {\n        var sectionRow = generateSectionRow(columns, 'foot');\n        if (sectionRow)\n            content.foot.push(sectionRow);\n    }\n    var theme = input.settings.theme;\n    var styles = input.styles;\n    return {\n        columns: columns,\n        head: parseSection('head', content.head, columns, styles, theme, sf),\n        body: parseSection('body', content.body, columns, styles, theme, sf),\n        foot: parseSection('foot', content.foot, columns, styles, theme, sf),\n    };\n}\nfunction parseSection(sectionName, sectionRows, columns, styleProps, theme, scaleFactor) {\n    var rowSpansLeftForColumn = {};\n    var result = sectionRows.map(function (rawRow, rowIndex) {\n        var skippedRowForRowSpans = 0;\n        var cells = {};\n        var colSpansAdded = 0;\n        var columnSpansLeft = 0;\n        for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n            var column = columns_1[_i];\n            if (rowSpansLeftForColumn[column.index] == null ||\n                rowSpansLeftForColumn[column.index].left === 0) {\n                if (columnSpansLeft === 0) {\n                    var rawCell = void 0;\n                    if (Array.isArray(rawRow)) {\n                        rawCell =\n                            rawRow[column.index - colSpansAdded - skippedRowForRowSpans];\n                    }\n                    else {\n                        rawCell = rawRow[column.dataKey];\n                    }\n                    var cellInputStyles = {};\n                    if (typeof rawCell === 'object' && !Array.isArray(rawCell)) {\n                        cellInputStyles = (rawCell === null || rawCell === void 0 ? void 0 : rawCell.styles) || {};\n                    }\n                    var styles = cellStyles(sectionName, column, rowIndex, theme, styleProps, scaleFactor, cellInputStyles);\n                    var cell = new Cell(rawCell, styles, sectionName);\n                    // dataKey is not used internally no more but keep for\n                    // backwards compat in hooks\n                    cells[column.dataKey] = cell;\n                    cells[column.index] = cell;\n                    columnSpansLeft = cell.colSpan - 1;\n                    rowSpansLeftForColumn[column.index] = {\n                        left: cell.rowSpan - 1,\n                        times: columnSpansLeft,\n                    };\n                }\n                else {\n                    columnSpansLeft--;\n                    colSpansAdded++;\n                }\n            }\n            else {\n                rowSpansLeftForColumn[column.index].left--;\n                columnSpansLeft = rowSpansLeftForColumn[column.index].times;\n                skippedRowForRowSpans++;\n            }\n        }\n        return new Row(rawRow, rowIndex, sectionName, cells);\n    });\n    return result;\n}\nfunction generateSectionRow(columns, section) {\n    var sectionRow = {};\n    columns.forEach(function (col) {\n        if (col.raw != null) {\n            var title = getSectionTitle(section, col.raw);\n            if (title != null)\n                sectionRow[col.dataKey] = title;\n        }\n    });\n    return Object.keys(sectionRow).length > 0 ? sectionRow : null;\n}\nfunction getSectionTitle(section, column) {\n    if (section === 'head') {\n        if (typeof column === 'object') {\n            return column.header || null;\n        }\n        else if (typeof column === 'string' || typeof column === 'number') {\n            return column;\n        }\n    }\n    else if (section === 'foot' && typeof column === 'object') {\n        return column.footer;\n    }\n    return null;\n}\nfunction createColumns(columns) {\n    return columns.map(function (input, index) {\n        var _a;\n        var key;\n        if (typeof input === 'object') {\n            key = (_a = input.dataKey) !== null && _a !== void 0 ? _a : index;\n        }\n        else {\n            key = index;\n        }\n        return new Column(key, input, index);\n    });\n}\nfunction cellStyles(sectionName, column, rowIndex, themeName, styles, scaleFactor, cellInputStyles) {\n    var theme = getTheme(themeName);\n    var sectionStyles;\n    if (sectionName === 'head') {\n        sectionStyles = styles.headStyles;\n    }\n    else if (sectionName === 'body') {\n        sectionStyles = styles.bodyStyles;\n    }\n    else if (sectionName === 'foot') {\n        sectionStyles = styles.footStyles;\n    }\n    var otherStyles = assign({}, theme.table, theme[sectionName], styles.styles, sectionStyles);\n    var columnStyles = styles.columnStyles[column.dataKey] ||\n        styles.columnStyles[column.index] ||\n        {};\n    var colStyles = sectionName === 'body' ? columnStyles : {};\n    var rowStyles = sectionName === 'body' && rowIndex % 2 === 0\n        ? assign({}, theme.alternateRow, styles.alternateRowStyles)\n        : {};\n    var defaultStyle = defaultStyles(scaleFactor);\n    var themeStyles = assign({}, defaultStyle, otherStyles, rowStyles, colStyles);\n    return assign(themeStyles, cellInputStyles);\n}\n\n// get columns can be fit into page\nfunction getColumnsCanFitInPage(doc, table, config) {\n    var _a;\n    if (config === void 0) { config = {}; }\n    // Get page width\n    var remainingWidth = getPageAvailableWidth(doc, table);\n    // Get column data key to repeat\n    var repeatColumnsMap = new Map();\n    var colIndexes = [];\n    var columns = [];\n    var horizontalPageBreakRepeat = [];\n    if (Array.isArray(table.settings.horizontalPageBreakRepeat)) {\n        horizontalPageBreakRepeat = table.settings.horizontalPageBreakRepeat;\n        // It can be a single value of type string or number (even number: 0)\n    }\n    else if (typeof table.settings.horizontalPageBreakRepeat === 'string' ||\n        typeof table.settings.horizontalPageBreakRepeat === 'number') {\n        horizontalPageBreakRepeat = [table.settings.horizontalPageBreakRepeat];\n    }\n    // Code to repeat the given column in split pages\n    horizontalPageBreakRepeat.forEach(function (field) {\n        var col = table.columns.find(function (item) { return item.dataKey === field || item.index === field; });\n        if (col && !repeatColumnsMap.has(col.index)) {\n            repeatColumnsMap.set(col.index, true);\n            colIndexes.push(col.index);\n            columns.push(table.columns[col.index]);\n            remainingWidth -= col.wrappedWidth;\n        }\n    });\n    var first = true;\n    var i = (_a = config === null || config === void 0 ? void 0 : config.start) !== null && _a !== void 0 ? _a : 0; // make sure couter is initiated outside the loop\n    while (i < table.columns.length) {\n        // Prevent duplicates\n        if (repeatColumnsMap.has(i)) {\n            i++;\n            continue;\n        }\n        var colWidth = table.columns[i].wrappedWidth;\n        // Take at least one column even if it doesn't fit\n        if (first || remainingWidth >= colWidth) {\n            first = false;\n            colIndexes.push(i);\n            columns.push(table.columns[i]);\n            remainingWidth -= colWidth;\n        }\n        else {\n            break;\n        }\n        i++;\n    }\n    return { colIndexes: colIndexes, columns: columns, lastIndex: i - 1 };\n}\nfunction calculateAllColumnsCanFitInPage(doc, table) {\n    var allResults = [];\n    for (var i = 0; i < table.columns.length; i++) {\n        var result = getColumnsCanFitInPage(doc, table, { start: i });\n        if (result.columns.length) {\n            allResults.push(result);\n            i = result.lastIndex;\n        }\n    }\n    return allResults;\n}\n\nfunction drawTable(jsPDFDoc, table) {\n    var settings = table.settings;\n    var startY = settings.startY;\n    var margin = settings.margin;\n    var cursor = { x: margin.left, y: startY };\n    var sectionsHeight = table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    var minTableBottomPos = startY + margin.bottom + sectionsHeight;\n    if (settings.pageBreak === 'avoid') {\n        var rows = table.body;\n        var tableHeight = rows.reduce(function (acc, row) { return acc + row.height; }, 0);\n        minTableBottomPos += tableHeight;\n    }\n    var doc = new DocHandler(jsPDFDoc);\n    if (settings.pageBreak === 'always' ||\n        (settings.startY != null && minTableBottomPos > doc.pageSize().height)) {\n        nextPage(doc);\n        cursor.y = margin.top;\n    }\n    table.callWillDrawPageHooks(doc, cursor);\n    var startPos = assign({}, cursor);\n    table.startPageNumber = doc.pageNumber();\n    if (settings.horizontalPageBreak) {\n        // managed flow for split columns\n        printTableWithHorizontalPageBreak(doc, table, startPos, cursor);\n    }\n    else {\n        // normal flow\n        doc.applyStyles(doc.userStyles);\n        if (settings.showHead === 'firstPage' ||\n            settings.showHead === 'everyPage') {\n            table.head.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n        doc.applyStyles(doc.userStyles);\n        table.body.forEach(function (row, index) {\n            var isLastRow = index === table.body.length - 1;\n            printFullRow(doc, table, row, isLastRow, startPos, cursor, table.columns);\n        });\n        doc.applyStyles(doc.userStyles);\n        if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n            table.foot.forEach(function (row) {\n                return printRow(doc, table, row, cursor, table.columns);\n            });\n        }\n    }\n    addTableBorder(doc, table, startPos, cursor);\n    table.callEndPageHooks(doc, cursor);\n    table.finalY = cursor.y;\n    jsPDFDoc.lastAutoTable = table;\n    doc.applyStyles(doc.userStyles);\n}\nfunction printTableWithHorizontalPageBreak(doc, table, startPos, cursor) {\n    // calculate width of columns and render only those which can fit into page\n    var allColumnsCanFitResult = calculateAllColumnsCanFitInPage(doc, table);\n    var settings = table.settings;\n    if (settings.horizontalPageBreakBehaviour === 'afterAllRows') {\n        allColumnsCanFitResult.forEach(function (colsAndIndexes, index) {\n            doc.applyStyles(doc.userStyles);\n            // add page to print next columns in new page\n            if (index > 0) {\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n            }\n            else {\n                // print head for selected columns\n                printHead(doc, table, cursor, colsAndIndexes.columns);\n            }\n            // print body & footer for selected columns\n            printBody(doc, table, startPos, cursor, colsAndIndexes.columns);\n            printFoot(doc, table, cursor, colsAndIndexes.columns);\n        });\n    }\n    else {\n        var lastRowIndexOfLastPage_1 = -1;\n        var firstColumnsToFitResult = allColumnsCanFitResult[0];\n        var _loop_1 = function () {\n            // Print the first columns, taking note of the last row printed\n            var lastPrintedRowIndex = lastRowIndexOfLastPage_1;\n            if (firstColumnsToFitResult) {\n                doc.applyStyles(doc.userStyles);\n                var firstColumnsToFit = firstColumnsToFitResult.columns;\n                if (lastRowIndexOfLastPage_1 >= 0) {\n                    // When adding a page here, make sure not to print the footers\n                    // because they were already printed before on this same loop\n                    addPage(doc, table, startPos, cursor, firstColumnsToFit, true);\n                }\n                else {\n                    printHead(doc, table, cursor, firstColumnsToFit);\n                }\n                lastPrintedRowIndex = printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, firstColumnsToFit);\n                printFoot(doc, table, cursor, firstColumnsToFit);\n            }\n            // Check how many rows were printed, so that the next columns would not print more rows than that\n            var maxNumberOfRows = lastPrintedRowIndex - lastRowIndexOfLastPage_1;\n            // Print the next columns, never exceding maxNumberOfRows\n            allColumnsCanFitResult.slice(1).forEach(function (colsAndIndexes) {\n                doc.applyStyles(doc.userStyles);\n                // When adding a page here, make sure not to print the footers\n                // because they were already printed before on this same loop\n                addPage(doc, table, startPos, cursor, colsAndIndexes.columns, true);\n                printBodyWithoutPageBreaks(doc, table, lastRowIndexOfLastPage_1 + 1, cursor, colsAndIndexes.columns, maxNumberOfRows);\n                printFoot(doc, table, cursor, colsAndIndexes.columns);\n            });\n            lastRowIndexOfLastPage_1 = lastPrintedRowIndex;\n        };\n        while (lastRowIndexOfLastPage_1 < table.body.length - 1) {\n            _loop_1();\n        }\n    }\n}\nfunction printHead(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showHead === 'firstPage' || settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction printBody(doc, table, startPos, cursor, columns) {\n    doc.applyStyles(doc.userStyles);\n    table.body.forEach(function (row, index) {\n        var isLastRow = index === table.body.length - 1;\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    });\n}\nfunction printBodyWithoutPageBreaks(doc, table, startRowIndex, cursor, columns, maxNumberOfRows) {\n    doc.applyStyles(doc.userStyles);\n    maxNumberOfRows = maxNumberOfRows !== null && maxNumberOfRows !== void 0 ? maxNumberOfRows : table.body.length;\n    var endRowIndex = Math.min(startRowIndex + maxNumberOfRows, table.body.length);\n    var lastPrintedRowIndex = -1;\n    table.body.slice(startRowIndex, endRowIndex).forEach(function (row, index) {\n        var isLastRow = startRowIndex + index === table.body.length - 1;\n        var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n        if (row.canEntireRowFit(remainingSpace, columns)) {\n            printRow(doc, table, row, cursor, columns);\n            lastPrintedRowIndex = startRowIndex + index;\n        }\n    });\n    return lastPrintedRowIndex;\n}\nfunction printFoot(doc, table, cursor, columns) {\n    var settings = table.settings;\n    doc.applyStyles(doc.userStyles);\n    if (settings.showFoot === 'lastPage' || settings.showFoot === 'everyPage') {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n}\nfunction getRemainingLineCount(cell, remainingPageSpace, doc) {\n    var lineHeight = doc.getLineHeight(cell.styles.fontSize);\n    var vPadding = cell.padding('vertical');\n    var remainingLines = Math.floor((remainingPageSpace - vPadding) / lineHeight);\n    return Math.max(0, remainingLines);\n}\nfunction modifyRowToFit(row, remainingPageSpace, table, doc) {\n    var cells = {};\n    row.spansMultiplePages = true;\n    row.height = 0;\n    var rowHeight = 0;\n    for (var _i = 0, _a = table.columns; _i < _a.length; _i++) {\n        var column = _a[_i];\n        var cell = row.cells[column.index];\n        if (!cell)\n            continue;\n        if (!Array.isArray(cell.text)) {\n            cell.text = [cell.text];\n        }\n        var remainderCell = new Cell(cell.raw, cell.styles, cell.section);\n        remainderCell = assign(remainderCell, cell);\n        remainderCell.text = [];\n        var remainingLineCount = getRemainingLineCount(cell, remainingPageSpace, doc);\n        if (cell.text.length > remainingLineCount) {\n            remainderCell.text = cell.text.splice(remainingLineCount, cell.text.length);\n        }\n        var scaleFactor = doc.scaleFactor();\n        var lineHeightFactor = doc.getLineHeightFactor();\n        cell.contentHeight = cell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (cell.contentHeight >= remainingPageSpace) {\n            cell.contentHeight = remainingPageSpace;\n            remainderCell.styles.minCellHeight -= remainingPageSpace;\n        }\n        if (cell.contentHeight > row.height) {\n            row.height = cell.contentHeight;\n        }\n        remainderCell.contentHeight = remainderCell.getContentHeight(scaleFactor, lineHeightFactor);\n        if (remainderCell.contentHeight > rowHeight) {\n            rowHeight = remainderCell.contentHeight;\n        }\n        cells[column.index] = remainderCell;\n    }\n    var remainderRow = new Row(row.raw, -1, row.section, cells, true);\n    remainderRow.height = rowHeight;\n    for (var _b = 0, _c = table.columns; _b < _c.length; _b++) {\n        var column = _c[_b];\n        var remainderCell = remainderRow.cells[column.index];\n        if (remainderCell) {\n            remainderCell.height = remainderRow.height;\n        }\n        var cell = row.cells[column.index];\n        if (cell) {\n            cell.height = row.height;\n        }\n    }\n    return remainderRow;\n}\nfunction shouldPrintOnCurrentPage(doc, row, remainingPageSpace, table) {\n    var pageHeight = doc.pageSize().height;\n    var margin = table.settings.margin;\n    var marginHeight = margin.top + margin.bottom;\n    var maxRowHeight = pageHeight - marginHeight;\n    if (row.section === 'body') {\n        // Should also take into account that head and foot is not\n        // on every page with some settings\n        maxRowHeight -=\n            table.getHeadHeight(table.columns) + table.getFootHeight(table.columns);\n    }\n    var minRowHeight = row.getMinimumRowHeight(table.columns, doc);\n    var minRowFits = minRowHeight < remainingPageSpace;\n    if (minRowHeight > maxRowHeight) {\n        console.error(\"Will not be able to print row \".concat(row.index, \" correctly since it's minimum height is larger than page height\"));\n        return true;\n    }\n    if (!minRowFits) {\n        return false;\n    }\n    var rowHasRowSpanCell = row.hasRowSpan(table.columns);\n    var rowHigherThanPage = row.getMaxCellHeight(table.columns) > maxRowHeight;\n    if (rowHigherThanPage) {\n        if (rowHasRowSpanCell) {\n            console.error(\"The content of row \".concat(row.index, \" will not be drawn correctly since drawing rows with a height larger than the page height and has cells with rowspans is not supported.\"));\n        }\n        return true;\n    }\n    if (rowHasRowSpanCell) {\n        // Currently a new page is required whenever a rowspan row don't fit a page.\n        return false;\n    }\n    if (table.settings.rowPageBreak === 'avoid') {\n        return false;\n    }\n    // In all other cases print the row on current page\n    return true;\n}\nfunction printFullRow(doc, table, row, isLastRow, startPos, cursor, columns) {\n    var remainingSpace = getRemainingPageSpace(doc, table, isLastRow, cursor);\n    if (row.canEntireRowFit(remainingSpace, columns)) {\n        // The row fits in the current page\n        printRow(doc, table, row, cursor, columns);\n    }\n    else if (shouldPrintOnCurrentPage(doc, row, remainingSpace, table)) {\n        // The row gets split in two here, each piece in one page\n        var remainderRow = modifyRowToFit(row, remainingSpace, table, doc);\n        printRow(doc, table, row, cursor, columns);\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, remainderRow, isLastRow, startPos, cursor, columns);\n    }\n    else {\n        // The row get printed entirelly on the next page\n        addPage(doc, table, startPos, cursor, columns);\n        printFullRow(doc, table, row, isLastRow, startPos, cursor, columns);\n    }\n}\nfunction printRow(doc, table, row, cursor, columns) {\n    cursor.x = table.settings.margin.left;\n    for (var _i = 0, columns_1 = columns; _i < columns_1.length; _i++) {\n        var column = columns_1[_i];\n        var cell = row.cells[column.index];\n        if (!cell) {\n            cursor.x += column.width;\n            continue;\n        }\n        doc.applyStyles(cell.styles);\n        cell.x = cursor.x;\n        cell.y = cursor.y;\n        var result = table.callCellHooks(doc, table.hooks.willDrawCell, cell, row, column, cursor);\n        if (result === false) {\n            cursor.x += column.width;\n            continue;\n        }\n        drawCellRect(doc, cell, cursor);\n        var textPos = cell.getTextPos();\n        autoTableText(cell.text, textPos.x, textPos.y, {\n            halign: cell.styles.halign,\n            valign: cell.styles.valign,\n            maxWidth: Math.ceil(cell.width - cell.padding('left') - cell.padding('right')),\n        }, doc.getDocument());\n        table.callCellHooks(doc, table.hooks.didDrawCell, cell, row, column, cursor);\n        cursor.x += column.width;\n    }\n    cursor.y += row.height;\n}\nfunction drawCellRect(doc, cell, cursor) {\n    var cellStyles = cell.styles;\n    // https://github.com/simonbengtsson/jsPDF-AutoTable/issues/774\n    // TODO (v4): better solution?\n    doc.getDocument().setFillColor(doc.getDocument().getFillColor());\n    if (typeof cellStyles.lineWidth === 'number') {\n        // Draw cell background with normal borders\n        var fillStyle = getFillStyle(cellStyles.lineWidth, cellStyles.fillColor);\n        if (fillStyle) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, fillStyle);\n        }\n    }\n    else if (typeof cellStyles.lineWidth === 'object') {\n        // Draw cell background\n        if (cellStyles.fillColor) {\n            doc.rect(cell.x, cursor.y, cell.width, cell.height, 'F');\n        }\n        // Draw cell individual borders\n        drawCellBorders(doc, cell, cursor, cellStyles.lineWidth);\n    }\n}\n/**\n * Draw all specified borders. Borders are centered on cell's edge and lengthened\n * to overlap with neighbours to create sharp corners.\n * @param doc\n * @param cell\n * @param cursor\n * @param fillColor\n * @param lineWidth\n */\nfunction drawCellBorders(doc, cell, cursor, lineWidth) {\n    var x1, y1, x2, y2;\n    if (lineWidth.top) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.top, x1, y1, x2, y2);\n    }\n    if (lineWidth.bottom) {\n        x1 = cursor.x;\n        y1 = cursor.y + cell.height;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.right) {\n            x2 += 0.5 * lineWidth.right;\n        }\n        if (lineWidth.left) {\n            x1 -= 0.5 * lineWidth.left;\n        }\n        drawLine(lineWidth.bottom, x1, y1, x2, y2);\n    }\n    if (lineWidth.left) {\n        x1 = cursor.x;\n        y1 = cursor.y;\n        x2 = cursor.x;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.left, x1, y1, x2, y2);\n    }\n    if (lineWidth.right) {\n        x1 = cursor.x + cell.width;\n        y1 = cursor.y;\n        x2 = cursor.x + cell.width;\n        y2 = cursor.y + cell.height;\n        if (lineWidth.top) {\n            y1 -= 0.5 * lineWidth.top;\n        }\n        if (lineWidth.bottom) {\n            y2 += 0.5 * lineWidth.bottom;\n        }\n        drawLine(lineWidth.right, x1, y1, x2, y2);\n    }\n    function drawLine(width, x1, y1, x2, y2) {\n        doc.getDocument().setLineWidth(width);\n        doc.getDocument().line(x1, y1, x2, y2, 'S');\n    }\n}\nfunction getRemainingPageSpace(doc, table, isLastRow, cursor) {\n    var bottomContentHeight = table.settings.margin.bottom;\n    var showFoot = table.settings.showFoot;\n    if (showFoot === 'everyPage' || (showFoot === 'lastPage' && isLastRow)) {\n        bottomContentHeight += table.getFootHeight(table.columns);\n    }\n    return doc.pageSize().height - cursor.y - bottomContentHeight;\n}\nfunction addPage(doc, table, startPos, cursor, columns, suppressFooter) {\n    if (columns === void 0) { columns = []; }\n    if (suppressFooter === void 0) { suppressFooter = false; }\n    doc.applyStyles(doc.userStyles);\n    if (table.settings.showFoot === 'everyPage' && !suppressFooter) {\n        table.foot.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n    }\n    // Add user content just before adding new page ensure it will\n    // be drawn above other things on the page\n    table.callEndPageHooks(doc, cursor);\n    var margin = table.settings.margin;\n    addTableBorder(doc, table, startPos, cursor);\n    nextPage(doc);\n    table.pageNumber++;\n    cursor.x = margin.left;\n    cursor.y = margin.top;\n    startPos.y = margin.top;\n    // call didAddPage hooks before any content is added to the page\n    table.callWillDrawPageHooks(doc, cursor);\n    if (table.settings.showHead === 'everyPage') {\n        table.head.forEach(function (row) { return printRow(doc, table, row, cursor, columns); });\n        doc.applyStyles(doc.userStyles);\n    }\n}\nfunction nextPage(doc) {\n    var current = doc.pageNumber();\n    doc.setPage(current + 1);\n    var newCurrent = doc.pageNumber();\n    if (newCurrent === current) {\n        doc.addPage();\n        return true;\n    }\n    return false;\n}\n\nfunction applyPlugin(jsPDF) {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    jsPDF.API.autoTable = function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var options = args[0];\n        var input = parseInput(this, options);\n        var table = createTable(this, input);\n        drawTable(this, table);\n        return this;\n    };\n    // Assign false to enable `doc.lastAutoTable.finalY || 40` sugar\n    jsPDF.API.lastAutoTable = false;\n    jsPDF.API.autoTableText = function (text, x, y, styles) {\n        autoTableText(text, x, y, styles, this);\n    };\n    jsPDF.API.autoTableSetDefaults = function (defaults) {\n        DocHandler.setDefaults(defaults, this);\n        return this;\n    };\n    jsPDF.autoTableSetDefaults = function (defaults, doc) {\n        DocHandler.setDefaults(defaults, doc);\n    };\n    jsPDF.API.autoTableHtmlToJson = function (tableElem, includeHiddenElements) {\n        var _a;\n        if (includeHiddenElements === void 0) { includeHiddenElements = false; }\n        if (typeof window === 'undefined') {\n            console.error('Cannot run autoTableHtmlToJson in non browser environment');\n            return null;\n        }\n        var doc = new DocHandler(this);\n        var _b = parseHtml(doc, tableElem, window, includeHiddenElements, false), head = _b.head, body = _b.body;\n        var columns = ((_a = head[0]) === null || _a === void 0 ? void 0 : _a.map(function (c) { return c.content; })) || [];\n        return { columns: columns, rows: body, data: body };\n    };\n}\n\nvar _a;\nfunction autoTable(d, options) {\n    var input = parseInput(d, options);\n    var table = createTable(d, input);\n    drawTable(d, table);\n}\n// Experimental export\nfunction __createTable(d, options) {\n    var input = parseInput(d, options);\n    return createTable(d, input);\n}\nfunction __drawTable(d, table) {\n    drawTable(d, table);\n}\ntry {\n    if (typeof window !== 'undefined' && window) {\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        var anyWindow = window;\n        var jsPDF = anyWindow.jsPDF || ((_a = anyWindow.jspdf) === null || _a === void 0 ? void 0 : _a.jsPDF);\n        if (jsPDF) {\n            applyPlugin(jsPDF);\n        }\n    }\n}\ncatch (error) {\n    console.error('Could not apply autoTable plugin', error);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.mjs\n");

/***/ })

};
;