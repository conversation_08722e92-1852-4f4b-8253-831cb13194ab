"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/admin/plans/page",{

/***/ "(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js":
/*!**************************************************!*\
  !*** ./src/app/modules/admin/plans/PlansPage.js ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,Loader2,Minus,Package,Plus,RefreshCw,Settings,Trash2,Users,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/subscriptionService */ \"(app-pages-browser)/./src/services/subscriptionService.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.js\");\n/* harmony import */ var _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/ToastContext */ \"(app-pages-browser)/./src/contexts/ToastContext.js\");\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/api */ \"(app-pages-browser)/./src/utils/api.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.js\");\n/* harmony import */ var _components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ModuleCard */ \"(app-pages-browser)/./src/components/ui/ModuleCard.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst PlansPage = ()=>{\n    var _subscription_modules;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showToast } = (0,_contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [plans, setPlans] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [invoices, setInvoices] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showCancelModal, setShowCancelModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showModuleModal, setShowModuleModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedModule, setSelectedModule] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [companies, setCompanies] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedCompanyId, setSelectedCompanyId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n                loadCompanies();\n            } else {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PlansPage.useEffect\": ()=>{\n            if (selectedCompanyId) {\n                loadData();\n            }\n        }\n    }[\"PlansPage.useEffect\"], [\n        selectedCompanyId\n    ]);\n    const loadCompanies = async ()=>{\n        try {\n            setIsLoading(true);\n            const response = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get('/companies?limit=100');\n            setCompanies(response.data.companies || []);\n        } catch (error) {\n            console.error('Erro ao carregar empresas:', error);\n            showToast('Erro ao carregar lista de empresas', 'error');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const loadData = async ()=>{\n        if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && !selectedCompanyId) {\n            return;\n        }\n        try {\n            setIsLoading(true);\n            // Para SYSTEM_ADMIN, adiciona o companyId como query parameter\n            const queryParams = (user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN' && selectedCompanyId ? \"?companyId=\".concat(selectedCompanyId) : '';\n            // Carregar dados com tratamento individual de erros\n            let subscriptionData = null;\n            let invoicesData = [];\n            let plansData = null;\n            try {\n                subscriptionData = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription\".concat(queryParams)).then((res)=>res.data);\n            } catch (error) {\n                var _error_response;\n                if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 404) {\n                    // Empresa não tem assinatura - isso é normal\n                    subscriptionData = null;\n                } else {\n                    throw error; // Re-throw outros erros\n                }\n            }\n            try {\n                invoicesData = await _utils_api__WEBPACK_IMPORTED_MODULE_5__.api.get(\"/subscription/invoices\".concat(queryParams)).then((res)=>res.data);\n            } catch (error) {\n                var _error_response1;\n                if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 404) {\n                    // Empresa não tem faturas - isso é normal\n                    invoicesData = {\n                        invoices: []\n                    };\n                } else {\n                    console.warn('Erro ao carregar faturas:', error);\n                    invoicesData = {\n                        invoices: []\n                    };\n                }\n            }\n            try {\n                plansData = await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.getPlans();\n            } catch (error) {\n                console.warn('Erro ao carregar planos:', error);\n                plansData = null;\n            }\n            setSubscription(subscriptionData);\n            setPlans(plansData);\n            setInvoices((invoicesData === null || invoicesData === void 0 ? void 0 : invoicesData.invoices) || []);\n        } catch (error) {\n            console.error('Erro ao carregar dados:', error);\n            if (showToast && typeof showToast === 'function') {\n                showToast('Erro ao carregar informações do plano', 'error');\n            }\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.addModule(moduleType);\n            showToast('Módulo adicionado com sucesso!', 'success');\n            await loadData();\n            setShowModuleModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao adicionar módulo:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao adicionar módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveModule = async (moduleType)=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.removeModule(moduleType);\n            showToast('Módulo removido com sucesso!', 'success');\n            await loadData();\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao remover módulo:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao remover módulo', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCancelSubscription = async ()=>{\n        try {\n            setActionLoading(true);\n            await _services_subscriptionService__WEBPACK_IMPORTED_MODULE_2__.subscriptionService.cancelSubscription();\n            showToast('Assinatura cancelada com sucesso!', 'success');\n            await loadData();\n            setShowCancelModal(false);\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error('Erro ao cancelar assinatura:', error);\n            showToast(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Erro ao cancelar assinatura', 'error');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'text-green-600 bg-green-100';\n            case 'TRIAL':\n                return 'text-blue-600 bg-blue-100';\n            case 'PAST_DUE':\n                return 'text-yellow-600 bg-yellow-100';\n            case 'CANCELED':\n                return 'text-red-600 bg-red-100';\n            default:\n                return 'text-gray-600 bg-gray-100';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'ACTIVE':\n                return 'Ativo';\n            case 'TRIAL':\n                return 'Período de Teste';\n            case 'PAST_DUE':\n                return 'Pagamento em Atraso';\n            case 'CANCELED':\n                return 'Cancelado';\n            default:\n                return status;\n        }\n    };\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('pt-BR');\n    };\n    const formatCurrency = (value)=>{\n        return new Intl.NumberFormat('pt-BR', {\n            style: 'currency',\n            currency: 'BRL'\n        }).format(value);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8 animate-spin text-admin-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Se for SYSTEM_ADMIN, mostrar seletor de empresa\n    if ((user === null || user === void 0 ? void 0 : user.role) === 'SYSTEM_ADMIN') {\n        var _subscription_modules1;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                    title: \"Gerenciamento de Planos\",\n                    description: \"Selecione uma empresa para gerenciar sua assinatura, m\\xf3dulos e faturamento\",\n                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        size: 20\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 221,\n                        columnNumber: 17\n                    }, void 0),\n                    moduleColor: \"admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    moduleColor: \"admin\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-4\",\n                                        children: \"Selecionar Empresa\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleSelect, {\n                                        value: selectedCompanyId || '',\n                                        onChange: (e)=>setSelectedCompanyId(e.target.value),\n                                        moduleColor: \"admin\",\n                                        className: \"w-full max-w-md\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Selecione uma empresa...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            companies.map((company)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: company.id,\n                                                    children: company.name\n                                                }, company.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 231,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 227,\n                                columnNumber: 13\n                            }, undefined),\n                            !selectedCompanyId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-admin-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 249,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"Selecione uma empresa acima para visualizar e gerenciar seus planos de assinatura.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 251,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, undefined),\n                            selectedCompanyId && !subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mx-auto w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-yellow-600 dark:text-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 260,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 259,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                        children: \"Nenhuma Assinatura Encontrada\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400\",\n                                        children: \"A empresa selecionada n\\xe3o possui uma assinatura ativa no momento.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 258,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 226,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, undefined),\n                selectedCompanyId && subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                className: \"lg:col-span-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: \"Status da Assinatura\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                                children: getStatusText(subscription.status)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 283,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"Ciclo de Faturamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: subscription.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 289,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"Valor Mensal\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: formatCurrency(subscription.pricePerMonth)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"Pr\\xf3ximo Pagamento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 301,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                        children: \"Data de In\\xedcio\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-gray-900 dark:text-white\",\n                                                        children: formatDate(subscription.startDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 307,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                                    children: \"A assinatura desta empresa ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 317,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 316,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                moduleColor: \"admin\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-6 w-6 text-admin-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 330,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 329,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                            children: \"M\\xf3dulos Ativos\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 332,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-admin-primary mb-4\",\n                                            children: ((_subscription_modules1 = subscription.modules) === null || _subscription_modules1 === void 0 ? void 0 : _subscription_modules1.filter((m)=>m.active).length) || 0\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setShowModuleModal(true),\n                                            moduleColor: \"admin\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Gerenciar M\\xf3dulos\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 338,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 328,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 327,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 277,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n            lineNumber: 217,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleHeader, {\n                title: \"Gerenciamento de Planos\",\n                description: \"Gerencie sua assinatura, m\\xf3dulos e faturamento\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    size: 20\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 361,\n                    columnNumber: 15\n                }, void 0),\n                moduleColor: \"admin\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined),\n            subscription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        className: \"lg:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                        children: \"Status da Assinatura\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(subscription.status)),\n                                        children: getStatusText(subscription.status)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Ciclo de Faturamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: subscription.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 381,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Valor Mensal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: formatCurrency(subscription.pricePerMonth)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 385,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Pr\\xf3ximo Pagamento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 392,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                children: \"Data de In\\xedcio\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: formatDate(subscription.startDate)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 378,\n                                columnNumber: 13\n                            }, undefined),\n                            subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                                            children: \"Sua assinatura ser\\xe1 cancelada no final do per\\xedodo atual.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 409,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 406,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 368,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        moduleColor: \"admin\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6 text-admin-primary\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 420,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"M\\xf3dulos Ativos\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-3xl font-bold text-admin-primary mb-4\",\n                                    children: ((_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.filter((m)=>m.active).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 425,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    onClick: ()=>setShowModuleModal(true),\n                                    moduleColor: \"admin\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 434,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Gerenciar M\\xf3dulos\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 418,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 417,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 367,\n                columnNumber: 9\n            }, undefined),\n            (subscription === null || subscription === void 0 ? void 0 : subscription.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"M\\xf3dulos da Assinatura\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                onClick: ()=>setShowModuleModal(true),\n                                moduleColor: \"admin\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 454,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Adicionar M\\xf3dulo\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 445,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                        children: subscription.modules.map((module)=>{\n                            var _plans_modules_module_moduleType, _plans_modules;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4 border rounded-lg \".concat(module.active ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900 dark:text-white\",\n                                                children: (plans === null || plans === void 0 ? void 0 : (_plans_modules = plans.modules) === null || _plans_modules === void 0 ? void 0 : (_plans_modules_module_moduleType = _plans_modules[module.moduleType]) === null || _plans_modules_module_moduleType === void 0 ? void 0 : _plans_modules_module_moduleType.name) || module.moduleType\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            module.active ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 474,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 476,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                        children: [\n                                            formatCurrency(module.pricePerMonth),\n                                            \"/m\\xeas\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 479,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    module.active && ![\n                                        'BASIC',\n                                        'ADMIN',\n                                        'SCHEDULING'\n                                    ].includes(module.moduleType) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>handleRemoveModule(module.moduleType),\n                                        disabled: actionLoading,\n                                        className: \"w-full text-red-600 border-red-200 hover:bg-red-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 490,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            \"Remover\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 483,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, module.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 461,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 459,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 444,\n                columnNumber: 9\n            }, undefined),\n            invoices.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                children: \"Hist\\xf3rico de Faturas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: loadData,\n                                disabled: isLoading,\n                                moduleColor: \"admin\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(isLoading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 514,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Atualizar\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 503,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        className: \"border-b border-gray-200 dark:border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Data\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 523,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Valor\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 526,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 529,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-left py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"Vencimento\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 532,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"text-right py-3 px-4 font-medium text-gray-900 dark:text-white\",\n                                                children: \"A\\xe7\\xf5es\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                lineNumber: 535,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                        lineNumber: 522,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 521,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    children: invoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"border-b border-gray-100 dark:border-gray-800\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 543,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatCurrency(invoice.amount)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(invoice.status === 'PAID' ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20' : invoice.status === 'PENDING' ? 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20' : 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'),\n                                                        children: invoice.status === 'PAID' ? 'Pago' : invoice.status === 'PENDING' ? 'Pendente' : 'Falhou'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 550,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-gray-900 dark:text-white\",\n                                                    children: formatDate(invoice.dueDate)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"py-3 px-4 text-right\",\n                                                    children: invoice.stripeInvoiceUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: invoice.stripeInvoiceUrl,\n                                                        target: \"_blank\",\n                                                        rel: \"noopener noreferrer\",\n                                                        className: \"inline-flex items-center text-admin-primary hover:text-admin-primary/80\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            \"Download\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 566,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, invoice.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 542,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 540,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 520,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                        lineNumber: 519,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 502,\n                columnNumber: 9\n            }, undefined),\n            subscription && subscription.status === 'ACTIVE' && !subscription.cancelAtPeriodEnd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ModuleCard__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 dark:text-white mb-2\",\n                                    children: \"Gerenciar Assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 dark:text-gray-400\",\n                                    children: \"Cancele sua assinatura ou fa\\xe7a altera\\xe7\\xf5es no seu plano\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 593,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 589,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                            variant: \"outline\",\n                            onClick: ()=>setShowCancelModal(true),\n                            className: \"text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 602,\n                                    columnNumber: 15\n                                }, undefined),\n                                \"Cancelar Assinatura\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 597,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 588,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 587,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showCancelModal,\n                onClose: ()=>setShowCancelModal(false),\n                title: \"Cancelar Assinatura\",\n                moduleColor: \"admin\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 618,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-yellow-800 dark:text-yellow-200\",\n                                            children: \"Aten\\xe7\\xe3o!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 620,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-yellow-700 dark:text-yellow-300\",\n                                            children: \"Ao cancelar, voc\\xea perder\\xe1 acesso aos recursos pagos no final do per\\xedodo atual.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 623,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 619,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 617,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Tem certeza de que deseja cancelar sua assinatura? Esta a\\xe7\\xe3o n\\xe3o pode ser desfeita.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 629,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowCancelModal(false),\n                                    disabled: actionLoading,\n                                    moduleColor: \"admin\",\n                                    children: \"Manter Assinatura\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 634,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                    onClick: handleCancelSubscription,\n                                    disabled: actionLoading,\n                                    className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                    children: [\n                                        actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-4 w-4 animate-spin mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 648,\n                                            columnNumber: 17\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 650,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        \"Cancelar Assinatura\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 642,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 633,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 616,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 610,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleModal, {\n                isOpen: showModuleModal,\n                onClose: ()=>setShowModuleModal(false),\n                title: \"Gerenciar M\\xf3dulos\",\n                moduleColor: \"admin\",\n                size: \"lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 dark:text-gray-400\",\n                            children: \"Adicione ou remova m\\xf3dulos da sua assinatura. Os m\\xf3dulos b\\xe1sicos n\\xe3o podem ser removidos.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 667,\n                            columnNumber: 11\n                        }, undefined),\n                        (plans === null || plans === void 0 ? void 0 : plans.modules) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: Object.entries(plans.modules).map((param)=>{\n                                let [moduleType, moduleInfo] = param;\n                                var _subscription_modules;\n                                const isActive = subscription === null || subscription === void 0 ? void 0 : (_subscription_modules = subscription.modules) === null || _subscription_modules === void 0 ? void 0 : _subscription_modules.find((m)=>m.moduleType === moduleType && m.active);\n                                const isBasic = [\n                                    'BASIC',\n                                    'ADMIN',\n                                    'SCHEDULING'\n                                ].includes(moduleType);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg \".concat(isActive ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20' : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 dark:text-white\",\n                                                    children: moduleInfo.name\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 687,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                isActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-5 w-5 text-green-600\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 36\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 686,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-400 mb-3\",\n                                            children: moduleInfo.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 693,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium text-gray-900 dark:text-white\",\n                                                    children: [\n                                                        formatCurrency(moduleInfo.monthlyPrice),\n                                                        \"/m\\xeas\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 698,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                !isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_6__.ModuleButton, {\n                                                    size: \"sm\",\n                                                    variant: isActive ? \"outline\" : \"default\",\n                                                    onClick: ()=>isActive ? handleRemoveModule(moduleType) : handleAddModule(moduleType),\n                                                    disabled: actionLoading,\n                                                    moduleColor: \"admin\",\n                                                    className: isActive ? \"text-red-600 border-red-200 hover:bg-red-50\" : \"\",\n                                                    children: actionLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                        lineNumber: 712,\n                                                        columnNumber: 29\n                                                    }, undefined) : isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Remover\"\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Calendar_CheckCircle_Clock_CreditCard_DollarSign_Download_Loader2_Minus_Package_Plus_RefreshCw_Settings_Trash2_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            \"Adicionar\"\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 703,\n                                                    columnNumber: 25\n                                                }, undefined),\n                                                isBasic && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: \"Inclu\\xeddo\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                            lineNumber: 697,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, moduleType, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                                    lineNumber: 678,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                            lineNumber: 672,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                    lineNumber: 666,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n                lineNumber: 659,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Programa\\xe7\\xe3o\\\\high-tide-systems-frontend\\\\src\\\\app\\\\modules\\\\admin\\\\plans\\\\PlansPage.js\",\n        lineNumber: 357,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PlansPage, \"FYFw7ke5hAgRFzLok0vR+p2JFGk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_ToastContext__WEBPACK_IMPORTED_MODULE_4__.useToast\n    ];\n});\n_c = PlansPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PlansPage);\nvar _c;\n$RefreshReg$(_c, \"PlansPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modules/admin/plans/PlansPage.js\n"));

/***/ })

});