'use client';

// Exportar todos os componentes UI
export { default as ModuleModal } from './ModuleModal';
export { default as ModalButton } from './ModalButton';
export { default as ModuleButton } from './ModalButton'; // ModuleButton é um alias para ModalButton
export { default as ModuleInput } from './ModuleInput';
export { default as ModuleSelect } from './ModuleSelect';
export { default as ModuleTextarea } from './ModuleTextarea';
export { default as ModuleLabel } from './ModuleLabel';
export { default as ModuleFormGroup } from './ModuleFormGroup';
export { default as ModuleMaskedInput } from './ModuleMaskedInput';
export { default as ModuleDatePicker } from './ModuleDatePicker';
export { default as CustomScrollArea } from './CustomScrollArea';
export { default as ModuleTable } from './ModuleTable';
export { default as ModuleHeader } from './ModuleHeader';
export { default as ModuleTabs } from './ModuleTabs';
export { default as ConfirmationDialog } from './ConfirmationDialog';
export { default as MultiSelect } from './multi-select';
export { default as ModuleCheckbox } from './ModuleCheckbox';
export { default as ModuleRadio } from './ModuleRadio';
export { default as ModuleRadioGroup } from './ModuleRadioGroup';
