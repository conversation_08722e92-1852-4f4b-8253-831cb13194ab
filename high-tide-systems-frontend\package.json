{"name": "high-tide-systems-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "node scripts/copy-docs.js && next dev", "dev:turbo": "node scripts/copy-docs.js && next dev --turbopack", "build": "node scripts/copy-docs.js && next build", "start": "next start", "lint": "next lint", "copy-docs": "node scripts/copy-docs.js"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.17", "@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^16.6.0", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-tabs": "^1.1.7", "@react-input/mask": "^2.0.4", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "framer-motion": "^12.7.4", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.474.0", "next": "15.1.6", "preact": "^10.26.6", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^19.0.0", "recharts": "^2.15.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1"}}