{"name": "high-tide-systems-backend", "version": "1.0.0", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "npx prisma migrate deploy && npx prisma generate && node src/server.js", "studio": "prisma studio", "swagger-autogen": "node src/swagger-autogen.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:auth": "jest tests/controllers/auth.test.js", "test:users": "jest tests/controllers/users.test.js", "test:companies": "jest tests/controllers/companies.test.js", "test:persons": "jest tests/controllers/persons.test.js", "test:schedulings": "jest tests/controllers/schedulings.test.js", "test:service-types": "jest tests/controllers/service-types.test.js", "test:professions": "jest tests/controllers/professions.test.js", "test:cache": "jest tests/utils/cache-service.test.js", "test:email": "jest tests/utils/email-service.test.js", "test:auth-middleware": "jest tests/utils/auth-middleware.test.js", "test:controllers": "jest tests/controllers", "test:utils": "jest tests/utils"}, "keywords": [], "author": "", "license": "ISC", "description": "", "prisma": {"seed": "node ./prisma/seed.js"}, "dependencies": {"@prisma/client": "^6.2.1", "@socket.io/redis-adapter": "^8.3.0", "axios": "^1.8.4", "bcryptjs": "^2.4.3", "connect-redis": "^7.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "express-xss-sanitizer": "^2.0.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "papaparse": "^5.5.3", "redis": "^4.7.0", "socket.io": "^4.8.1", "stripe": "^17.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"@types/jest": "^29.5.14", "jest": "^29.7.0", "nodemon": "^3.1.9", "prisma": "^6.2.1", "supertest": "^7.1.0"}}