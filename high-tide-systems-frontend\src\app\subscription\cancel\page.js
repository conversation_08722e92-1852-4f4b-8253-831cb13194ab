"use client";

import { <PERSON><PERSON><PERSON>cle, ArrowLeft, Home, Mail, Phone } from "lucide-react";
import Link from "next/link";

export default function SubscriptionCancelPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 max-w-2xl w-full">
        {/* Cancel Icon */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <XCircle className="w-10 h-10 text-red-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Pagamento cancelado
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400">
            Não se preocupe, nenhuma cobrança foi realizada.
          </p>
        </div>

        {/* What happened */}
        <div className="bg-gray-50 rounded-xl p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            O que aconteceu?
          </h3>
          <div className="space-y-3 text-gray-600">
            <p>
              Você cancelou o processo de pagamento antes da conclusão. Isso pode ter acontecido por diversos motivos:
            </p>
            <ul className="list-disc list-inside space-y-1 ml-4">
              <li>Você decidiu revisar os dados antes de finalizar</li>
              <li>Houve algum problema com o método de pagamento</li>
              <li>Você fechou a janela acidentalmente</li>
              <li>Você mudou de ideia sobre o plano escolhido</li>
            </ul>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-orange-50 border border-orange-200 rounded-xl p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            O que você pode fazer agora?
          </h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                1
              </div>
              <div>
                <p className="font-medium text-gray-900">Tentar novamente</p>
                <p className="text-sm text-gray-600">
                  Você pode retornar ao processo de assinatura e tentar novamente quando estiver pronto.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                2
              </div>
              <div>
                <p className="font-medium text-gray-900">Explorar mais sobre o sistema</p>
                <p className="text-sm text-gray-600">
                  Volte à nossa página inicial para conhecer melhor todos os recursos disponíveis.
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium mr-3 mt-0.5">
                3
              </div>
              <div>
                <p className="font-medium text-gray-900">Falar com nossa equipe</p>
                <p className="text-sm text-gray-600">
                  Se você tem dúvidas sobre os planos ou precisa de ajuda, nossa equipe está pronta para atendê-lo.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Special Offer */}
        <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl p-6 text-white mb-8">
          <h3 className="text-lg font-semibold mb-2">
            Oferta especial para você!
          </h3>
          <p className="text-orange-100 mb-4">
            Que tal agendar uma demonstração gratuita? Nossa equipe pode mostrar como o High Tide Systems 
            pode transformar a gestão da sua clínica ou consultório.
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <a
              href="mailto:<EMAIL>?subject=Demonstração%20Gratuita"
              className="inline-flex items-center justify-center px-4 py-2 bg-white text-orange-600 rounded-lg hover:bg-orange-50 transition-colors font-medium"
            >
              <Mail className="w-4 h-4 mr-2" />
              Agendar por email
            </a>
            <a
              href="tel:+5511999999999"
              className="inline-flex items-center justify-center px-4 py-2 bg-orange-400 text-white rounded-lg hover:bg-orange-300 transition-colors font-medium"
            >
              <Phone className="w-4 h-4 mr-2" />
              Ligar agora
            </a>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <Link
            href="/subscription/signup"
            className="flex-1 inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-orange-500 hover:bg-orange-600 transition-colors"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Tentar novamente
          </Link>
          <Link
            href="/landing"
            className="flex-1 inline-flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <Home className="w-5 h-5 mr-2" />
            Voltar ao início
          </Link>
        </div>

        {/* FAQ Section */}
        <div className="mt-8 pt-8 border-t border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Perguntas frequentes
          </h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-gray-900">
                Meus dados foram salvos?
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                Não, nenhum dado pessoal ou de pagamento foi armazenado. Você precisará inserir as informações novamente se decidir continuar.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">
                Posso escolher um plano diferente?
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                Sim! Você pode explorar todos os nossos planos e módulos disponíveis antes de fazer sua escolha.
              </p>
            </div>
            <div>
              <h4 className="font-medium text-gray-900">
                Há algum desconto disponível?
              </h4>
              <p className="text-sm text-gray-600 mt-1">
                Oferecemos desconto para pagamento anual e também temos promoções especiais. Entre em contato para saber mais.
              </p>
            </div>
          </div>
        </div>

        {/* Support Info */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600">
            Precisa de ajuda? Entre em contato conosco pelo email{" "}
            <a href="mailto:<EMAIL>" className="text-orange-600 hover:text-orange-700">
              <EMAIL>
            </a>
            {" "}ou pelo telefone{" "}
            <a href="tel:+5511999999999" className="text-orange-600 hover:text-orange-700">
              (11) 99999-9999
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}
