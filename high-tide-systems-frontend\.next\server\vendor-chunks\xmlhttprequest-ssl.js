/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlhttprequest-ssl";
exports.ids = ["vendor-chunks/xmlhttprequest-ssl"];
exports.modules = {

/***/ "(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js":
/*!***************************************************************!*\
  !*** ./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * Wrapper for built-in http.js to emulate the browser XMLHttpRequest object.\n *\n * This can be used with JS designed for browsers to improve reuse of code and\n * allow the use of existing libraries.\n *\n * Usage: include(\"XMLHttpRequest.js\") and use XMLHttpRequest per W3C specs.\n *\n * <AUTHOR> DeFelippi <<EMAIL>>\n * @contributor David Ellis <<EMAIL>>\n * @license MIT\n */\n\nvar fs = __webpack_require__(/*! fs */ \"fs\");\nvar Url = __webpack_require__(/*! url */ \"url\");\nvar spawn = (__webpack_require__(/*! child_process */ \"child_process\").spawn);\n\n/**\n * Module exports.\n */\n\nmodule.exports = XMLHttpRequest;\n\n// backwards-compat\nXMLHttpRequest.XMLHttpRequest = XMLHttpRequest;\n\n/**\n * `XMLHttpRequest` constructor.\n *\n * Supported options for the `opts` object are:\n *\n *  - `agent`: An http.Agent instance; http.globalAgent may be used; if 'undefined', agent usage is disabled\n *\n * @param {Object} opts optional \"options\" object\n */\n\nfunction XMLHttpRequest(opts) {\n  \"use strict\";\n\n  opts = opts || {};\n\n  /**\n   * Private variables\n   */\n  var self = this;\n  var http = __webpack_require__(/*! http */ \"http\");\n  var https = __webpack_require__(/*! https */ \"https\");\n\n  // Holds http.js objects\n  var request;\n  var response;\n\n  // Request settings\n  var settings = {};\n\n  // Disable header blacklist.\n  // Not part of XHR specs.\n  var disableHeaderCheck = false;\n\n  // Set some default headers\n  var defaultHeaders = {\n    \"User-Agent\": \"node-XMLHttpRequest\",\n    \"Accept\": \"*/*\"\n  };\n\n  var headers = Object.assign({}, defaultHeaders);\n\n  // These headers are not user setable.\n  // The following are allowed but banned in the spec:\n  // * user-agent\n  var forbiddenRequestHeaders = [\n    \"accept-charset\",\n    \"accept-encoding\",\n    \"access-control-request-headers\",\n    \"access-control-request-method\",\n    \"connection\",\n    \"content-length\",\n    \"content-transfer-encoding\",\n    \"cookie\",\n    \"cookie2\",\n    \"date\",\n    \"expect\",\n    \"host\",\n    \"keep-alive\",\n    \"origin\",\n    \"referer\",\n    \"te\",\n    \"trailer\",\n    \"transfer-encoding\",\n    \"upgrade\",\n    \"via\"\n  ];\n\n  // These request methods are not allowed\n  var forbiddenRequestMethods = [\n    \"TRACE\",\n    \"TRACK\",\n    \"CONNECT\"\n  ];\n\n  // Send flag\n  var sendFlag = false;\n  // Error flag, used when errors occur or abort is called\n  var errorFlag = false;\n  var abortedFlag = false;\n\n  // Event listeners\n  var listeners = {};\n\n  /**\n   * Constants\n   */\n\n  this.UNSENT = 0;\n  this.OPENED = 1;\n  this.HEADERS_RECEIVED = 2;\n  this.LOADING = 3;\n  this.DONE = 4;\n\n  /**\n   * Public vars\n   */\n\n  // Current state\n  this.readyState = this.UNSENT;\n\n  // default ready state change handler in case one is not set or is set late\n  this.onreadystatechange = null;\n\n  // Result & response\n  this.responseText = \"\";\n  this.responseXML = \"\";\n  this.response = Buffer.alloc(0);\n  this.status = null;\n  this.statusText = null;\n\n  /**\n   * Private methods\n   */\n\n  /**\n   * Check if the specified header is allowed.\n   *\n   * @param string header Header to validate\n   * @return boolean False if not allowed, otherwise true\n   */\n  var isAllowedHttpHeader = function(header) {\n    return disableHeaderCheck || (header && forbiddenRequestHeaders.indexOf(header.toLowerCase()) === -1);\n  };\n\n  /**\n   * Check if the specified method is allowed.\n   *\n   * @param string method Request method to validate\n   * @return boolean False if not allowed, otherwise true\n   */\n  var isAllowedHttpMethod = function(method) {\n    return (method && forbiddenRequestMethods.indexOf(method) === -1);\n  };\n\n  /**\n   * Public methods\n   */\n\n  /**\n   * Open the connection. Currently supports local server requests.\n   *\n   * @param string method Connection method (eg GET, POST)\n   * @param string url URL for the connection.\n   * @param boolean async Asynchronous connection. Default is true.\n   * @param string user Username for basic authentication (optional)\n   * @param string password Password for basic authentication (optional)\n   */\n  this.open = function(method, url, async, user, password) {\n    this.abort();\n    errorFlag = false;\n    abortedFlag = false;\n\n    // Check for valid request method\n    if (!isAllowedHttpMethod(method)) {\n      throw new Error(\"SecurityError: Request method not allowed\");\n    }\n\n    settings = {\n      \"method\": method,\n      \"url\": url.toString(),\n      \"async\": (typeof async !== \"boolean\" ? true : async),\n      \"user\": user || null,\n      \"password\": password || null\n    };\n\n    setState(this.OPENED);\n  };\n\n  /**\n   * Disables or enables isAllowedHttpHeader() check the request. Enabled by default.\n   * This does not conform to the W3C spec.\n   *\n   * @param boolean state Enable or disable header checking.\n   */\n  this.setDisableHeaderCheck = function(state) {\n    disableHeaderCheck = state;\n  };\n\n  /**\n   * Sets a header for the request.\n   *\n   * @param string header Header name\n   * @param string value Header value\n   * @return boolean Header added\n   */\n  this.setRequestHeader = function(header, value) {\n    if (this.readyState != this.OPENED) {\n      throw new Error(\"INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN\");\n    }\n    if (!isAllowedHttpHeader(header)) {\n      console.warn('Refused to set unsafe header \"' + header + '\"');\n      return false;\n    }\n    if (sendFlag) {\n      throw new Error(\"INVALID_STATE_ERR: send flag is true\");\n    }\n    headers[header] = value;\n    return true;\n  };\n\n  /**\n   * Gets a header from the server response.\n   *\n   * @param string header Name of header to get.\n   * @return string Text of the header or null if it doesn't exist.\n   */\n  this.getResponseHeader = function(header) {\n    if (typeof header === \"string\"\n      && this.readyState > this.OPENED\n      && response.headers[header.toLowerCase()]\n      && !errorFlag\n    ) {\n      return response.headers[header.toLowerCase()];\n    }\n\n    return null;\n  };\n\n  /**\n   * Gets all the response headers.\n   *\n   * @return string A string with all response headers separated by CR+LF\n   */\n  this.getAllResponseHeaders = function() {\n    if (this.readyState < this.HEADERS_RECEIVED || errorFlag) {\n      return \"\";\n    }\n    var result = \"\";\n\n    for (var i in response.headers) {\n      // Cookie headers are excluded\n      if (i !== \"set-cookie\" && i !== \"set-cookie2\") {\n        result += i + \": \" + response.headers[i] + \"\\r\\n\";\n      }\n    }\n    return result.substr(0, result.length - 2);\n  };\n\n  /**\n   * Gets a request header\n   *\n   * @param string name Name of header to get\n   * @return string Returns the request header or empty string if not set\n   */\n  this.getRequestHeader = function(name) {\n    // @TODO Make this case insensitive\n    if (typeof name === \"string\" && headers[name]) {\n      return headers[name];\n    }\n\n    return \"\";\n  };\n\n  /**\n   * Sends the request to the server.\n   *\n   * @param string data Optional data to send as request body.\n   */\n  this.send = function(data) {\n    if (this.readyState != this.OPENED) {\n      throw new Error(\"INVALID_STATE_ERR: connection must be opened before send() is called\");\n    }\n\n    if (sendFlag) {\n      throw new Error(\"INVALID_STATE_ERR: send has already been called\");\n    }\n\n    var ssl = false, local = false;\n    var url = Url.parse(settings.url);\n    var host;\n    // Determine the server\n    switch (url.protocol) {\n      case 'https:':\n        ssl = true;\n        // SSL & non-SSL both need host, no break here.\n      case 'http:':\n        host = url.hostname;\n        break;\n\n      case 'file:':\n        local = true;\n        break;\n\n      case undefined:\n      case '':\n        host = \"localhost\";\n        break;\n\n      default:\n        throw new Error(\"Protocol not supported.\");\n    }\n\n    // Load files off the local filesystem (file://)\n    if (local) {\n      if (settings.method !== \"GET\") {\n        throw new Error(\"XMLHttpRequest: Only GET method is supported\");\n      }\n\n      if (settings.async) {\n        fs.readFile(unescape(url.pathname), function(error, data) {\n          if (error) {\n            self.handleError(error, error.errno || -1);\n          } else {\n            self.status = 200;\n            self.responseText = data.toString('utf8');\n            self.response = data;\n            setState(self.DONE);\n          }\n        });\n      } else {\n        try {\n          this.response = fs.readFileSync(unescape(url.pathname));\n          this.responseText = this.response.toString('utf8');\n          this.status = 200;\n          setState(self.DONE);\n        } catch(e) {\n          this.handleError(e, e.errno || -1);\n        }\n      }\n\n      return;\n    }\n\n    // Default to port 80. If accessing localhost on another port be sure\n    // to use http://localhost:port/path\n    var port = url.port || (ssl ? 443 : 80);\n    // Add query string if one is used\n    var uri = url.pathname + (url.search ? url.search : '');\n\n    // Set the Host header or the server may reject the request\n    headers[\"Host\"] = host;\n    if (!((ssl && port === 443) || port === 80)) {\n      headers[\"Host\"] += ':' + url.port;\n    }\n\n    // Set Basic Auth if necessary\n    if (settings.user) {\n      if (typeof settings.password == \"undefined\") {\n        settings.password = \"\";\n      }\n      var authBuf = new Buffer(settings.user + \":\" + settings.password);\n      headers[\"Authorization\"] = \"Basic \" + authBuf.toString(\"base64\");\n    }\n\n    // Set content length header\n    if (settings.method === \"GET\" || settings.method === \"HEAD\") {\n      data = null;\n    } else if (data) {\n      headers[\"Content-Length\"] = Buffer.isBuffer(data) ? data.length : Buffer.byteLength(data);\n\n      var headersKeys = Object.keys(headers);\n      if (!headersKeys.some(function (h) { return h.toLowerCase() === 'content-type' })) {\n        headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\n      }\n    } else if (settings.method === \"POST\") {\n      // For a post with no data set Content-Length: 0.\n      // This is required by buggy servers that don't meet the specs.\n      headers[\"Content-Length\"] = 0;\n    }\n\n    var agent = opts.agent || false;\n    var options = {\n      host: host,\n      port: port,\n      path: uri,\n      method: settings.method,\n      headers: headers,\n      agent: agent\n    };\n\n    if (ssl) {\n      options.pfx = opts.pfx;\n      options.key = opts.key;\n      options.passphrase = opts.passphrase;\n      options.cert = opts.cert;\n      options.ca = opts.ca;\n      options.ciphers = opts.ciphers;\n      options.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n    }\n\n    // Reset error flag\n    errorFlag = false;\n    // Handle async requests\n    if (settings.async) {\n      // Use the proper protocol\n      var doRequest = ssl ? https.request : http.request;\n\n      // Request is being sent, set send flag\n      sendFlag = true;\n\n      // As per spec, this is called here for historical reasons.\n      self.dispatchEvent(\"readystatechange\");\n\n      // Handler for the response\n      var responseHandler = function(resp) {\n        // Set response var to the response we got back\n        // This is so it remains accessable outside this scope\n        response = resp;\n        // Check for redirect\n        // @TODO Prevent looped redirects\n        if (response.statusCode === 302 || response.statusCode === 303 || response.statusCode === 307) {\n          // Change URL to the redirect location\n          settings.url = response.headers.location;\n          var url = Url.parse(settings.url);\n          // Set host var in case it's used later\n          host = url.hostname;\n          // Options for the new request\n          var newOptions = {\n            hostname: url.hostname,\n            port: url.port,\n            path: url.path,\n            method: response.statusCode === 303 ? 'GET' : settings.method,\n            headers: headers\n          };\n\n          if (ssl) {\n            newOptions.pfx = opts.pfx;\n            newOptions.key = opts.key;\n            newOptions.passphrase = opts.passphrase;\n            newOptions.cert = opts.cert;\n            newOptions.ca = opts.ca;\n            newOptions.ciphers = opts.ciphers;\n            newOptions.rejectUnauthorized = opts.rejectUnauthorized === false ? false : true;\n          }\n\n          // Issue the new request\n          request = doRequest(newOptions, responseHandler).on('error', errorHandler);\n          request.end();\n          // @TODO Check if an XHR event needs to be fired here\n          return;\n        }\n\n        setState(self.HEADERS_RECEIVED);\n        self.status = response.statusCode;\n\n        response.on('data', function(chunk) {\n          // Make sure there's some data\n          if (chunk) {\n            var data = Buffer.from(chunk);\n            self.response = Buffer.concat([self.response, data]);\n          }\n          // Don't emit state changes if the connection has been aborted.\n          if (sendFlag) {\n            setState(self.LOADING);\n          }\n        });\n\n        response.on('end', function() {\n          if (sendFlag) {\n            // The sendFlag needs to be set before setState is called.  Otherwise if we are chaining callbacks\n            // there can be a timing issue (the callback is called and a new call is made before the flag is reset).\n            sendFlag = false;\n            // Discard the 'end' event if the connection has been aborted\n            setState(self.DONE);\n            // Construct responseText from response\n            self.responseText = self.response.toString('utf8');\n          }\n        });\n\n        response.on('error', function(error) {\n          self.handleError(error);\n        });\n      }\n\n      // Error handler for the request\n      var errorHandler = function(error) {\n        // In the case of https://nodejs.org/api/http.html#requestreusedsocket triggering an ECONNRESET,\n        // don't fail the xhr request, attempt again.\n        if (request.reusedSocket && error.code === 'ECONNRESET')\n          return doRequest(options, responseHandler).on('error', errorHandler);\n        self.handleError(error);\n      }\n\n      // Create the request\n      request = doRequest(options, responseHandler).on('error', errorHandler);\n\n      if (opts.autoUnref) {\n        request.on('socket', (socket) => {\n          socket.unref();\n        });\n      }\n\n      // Node 0.4 and later won't accept empty data. Make sure it's needed.\n      if (data) {\n        request.write(data);\n      }\n\n      request.end();\n\n      self.dispatchEvent(\"loadstart\");\n    } else { // Synchronous\n      // Create a temporary file for communication with the other Node process\n      var contentFile = \".node-xmlhttprequest-content-\" + process.pid;\n      var syncFile = \".node-xmlhttprequest-sync-\" + process.pid;\n      fs.writeFileSync(syncFile, \"\", \"utf8\");\n      // The async request the other Node process executes\n      var execString = \"var http = require('http'), https = require('https'), fs = require('fs');\"\n        + \"var doRequest = http\" + (ssl ? \"s\" : \"\") + \".request;\"\n        + \"var options = \" + JSON.stringify(options) + \";\"\n        + \"var responseText = '';\"\n        + \"var responseData = Buffer.alloc(0);\"\n        + \"var req = doRequest(options, function(response) {\"\n        + \"response.on('data', function(chunk) {\"\n        + \"  var data = Buffer.from(chunk);\"\n        + \"  responseText += data.toString('utf8');\"\n        + \"  responseData = Buffer.concat([responseData, data]);\"\n        + \"});\"\n        + \"response.on('end', function() {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + \"response.on('error', function(error) {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + \"}).on('error', function(error) {\"\n        + \"fs.writeFileSync('\" + contentFile + \"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');\"\n        + \"fs.unlinkSync('\" + syncFile + \"');\"\n        + \"});\"\n        + (data ? \"req.write('\" + JSON.stringify(data).slice(1,-1).replace(/'/g, \"\\\\'\") + \"');\":\"\")\n        + \"req.end();\";\n      // Start the other Node Process, executing this string\n      var syncProc = spawn(process.argv[0], [\"-e\", execString]);\n      var statusText;\n      while(fs.existsSync(syncFile)) {\n        // Wait while the sync file is empty\n      }\n      self.responseText = fs.readFileSync(contentFile, 'utf8');\n      // Kill the child process once the file has data\n      syncProc.stdin.end();\n      // Remove the temporary file\n      fs.unlinkSync(contentFile);\n      if (self.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)) {\n        // If the file returned an error, handle it\n        var errorObj = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/, \"\"));\n        self.handleError(errorObj, 503);\n      } else {\n        // If the file returned okay, parse its data and move to the DONE state\n        self.status = self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/, \"$1\");\n        var resp = JSON.parse(self.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/, \"$1\"));\n        response = {\n          statusCode: self.status,\n          headers: resp.data.headers\n        };\n        self.responseText = resp.data.text;\n        self.response = Buffer.from(resp.data.data, 'base64');\n        setState(self.DONE, true);\n      }\n    }\n  };\n\n  /**\n   * Called when an error is encountered to deal with it.\n   * @param  status  {number}    HTTP status code to use rather than the default (0) for XHR errors.\n   */\n  this.handleError = function(error, status) {\n    this.status = status || 0;\n    this.statusText = error;\n    this.responseText = error.stack;\n    errorFlag = true;\n    setState(this.DONE);\n  };\n\n  /**\n   * Aborts a request.\n   */\n  this.abort = function() {\n    if (request) {\n      request.abort();\n      request = null;\n    }\n\n    headers = Object.assign({}, defaultHeaders);\n    this.responseText = \"\";\n    this.responseXML = \"\";\n    this.response = Buffer.alloc(0);\n\n    errorFlag = abortedFlag = true\n    if (this.readyState !== this.UNSENT\n        && (this.readyState !== this.OPENED || sendFlag)\n        && this.readyState !== this.DONE) {\n      sendFlag = false;\n      setState(this.DONE);\n    }\n    this.readyState = this.UNSENT;\n  };\n\n  /**\n   * Adds an event listener. Preferred method of binding to events.\n   */\n  this.addEventListener = function(event, callback) {\n    if (!(event in listeners)) {\n      listeners[event] = [];\n    }\n    // Currently allows duplicate callbacks. Should it?\n    listeners[event].push(callback);\n  };\n\n  /**\n   * Remove an event callback that has already been bound.\n   * Only works on the matching funciton, cannot be a copy.\n   */\n  this.removeEventListener = function(event, callback) {\n    if (event in listeners) {\n      // Filter will return a new array with the callback removed\n      listeners[event] = listeners[event].filter(function(ev) {\n        return ev !== callback;\n      });\n    }\n  };\n\n  /**\n   * Dispatch any events, including both \"on\" methods and events attached using addEventListener.\n   */\n  this.dispatchEvent = function (event) {\n    if (typeof self[\"on\" + event] === \"function\") {\n      if (this.readyState === this.DONE && settings.async)\n        setTimeout(function() { self[\"on\" + event]() }, 0)\n      else\n        self[\"on\" + event]()\n    }\n    if (event in listeners) {\n      for (let i = 0, len = listeners[event].length; i < len; i++) {\n        if (this.readyState === this.DONE)\n          setTimeout(function() { listeners[event][i].call(self) }, 0)\n        else\n          listeners[event][i].call(self)\n      }\n    }\n  };\n\n  /**\n   * Changes readyState and calls onreadystatechange.\n   *\n   * @param int state New state\n   */\n  var setState = function(state) {\n    if ((self.readyState === state) || (self.readyState === self.UNSENT && abortedFlag))\n      return\n\n    self.readyState = state;\n\n    if (settings.async || self.readyState < self.OPENED || self.readyState === self.DONE) {\n      self.dispatchEvent(\"readystatechange\");\n    }\n\n    if (self.readyState === self.DONE) {\n      let fire\n\n      if (abortedFlag)\n        fire = \"abort\"\n      else if (errorFlag)\n        fire = \"error\"\n      else\n        fire = \"load\"\n\n      self.dispatchEvent(fire)\n\n      // @TODO figure out InspectorInstrumentation::didLoadXHR(cookie)\n      self.dispatchEvent(\"loadend\");\n    }\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/xmlhttprequest-ssl/lib/XMLHttpRequest.js\n");

/***/ })

};
;