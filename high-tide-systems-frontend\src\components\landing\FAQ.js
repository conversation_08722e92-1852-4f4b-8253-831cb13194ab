'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronDown, ChevronUp, HelpCircle, Shield, Clock, Smartphone, Settings, CreditCard } from 'lucide-react';
import { scrollToElement } from '@/utils/scrollUtils';

const faqCategories = [
  {
    id: 'general',
    name: 'G<PERSON>',
    icon: <HelpCircle className="h-5 w-5" />,
    color: 'text-primary-500 dark:text-primary-400'
  },
  {
    id: 'security',
    name: '<PERSON><PERSON><PERSON><PERSON>',
    icon: <Shield className="h-5 w-5" />,
    color: 'text-red-500 dark:text-red-400'
  },
  {
    id: 'implementation',
    name: 'Implementa<PERSON>',
    icon: <Clock className="h-5 w-5" />,
    color: 'text-amber-500 dark:text-amber-400'
  },
  {
    id: 'technical',
    name: 'Técnico',
    icon: <Smartphone className="h-5 w-5" />,
    color: 'text-purple-500 dark:text-purple-400'
  },
  {
    id: 'customization',
    name: 'Personaliza<PERSON>',
    icon: <Settings className="h-5 w-5" />,
    color: 'text-emerald-500 dark:text-emerald-400'
  },
  {
    id: 'pricing',
    name: 'Preços',
    icon: <CreditCard className="h-5 w-5" />,
    color: 'text-blue-500 dark:text-blue-400'
  }
];

const faqs = [
  {
    id: 1,
    category: 'general',
    question: "Como o High Tide Systems pode ajudar minha clínica?",
    answer: "O High Tide Systems oferece uma solução completa para gestão de clínicas e consultórios, integrando agendamentos, cadastro de pacientes, financeiro, relatórios e muito mais. Nossa plataforma ajuda a reduzir o tempo gasto com tarefas administrativas, minimizar erros, melhorar a comunicação entre a equipe e proporcionar uma experiência superior aos pacientes."
  },
  {
    id: 2,
    category: 'implementation',
    question: "Quanto tempo leva para implementar o sistema?",
    answer: "A implementação do High Tide Systems é rápida e simples. Após a contratação, você pode começar a usar o sistema em apenas 24 horas. Oferecemos treinamento completo para sua equipe e suporte durante todo o processo de implementação. A maioria das clínicas consegue estar totalmente operacional com o sistema em menos de uma semana."
  },
  {
    id: 3,
    category: 'security',
    question: "O sistema é seguro e atende às normas de proteção de dados?",
    answer: "Sim, o High Tide Systems foi desenvolvido com foco na segurança e privacidade dos dados. Estamos em conformidade com a LGPD (Lei Geral de Proteção de Dados) e utilizamos tecnologias avançadas de criptografia e proteção de dados. Realizamos backups regulares e implementamos controles de acesso rigorosos para garantir a segurança das informações."
  },
  {
    id: 4,
    category: 'technical',
    question: "Posso acessar o sistema de qualquer dispositivo?",
    answer: "Sim, o High Tide Systems é uma plataforma baseada em nuvem, o que significa que você pode acessá-la de qualquer dispositivo com conexão à internet, incluindo computadores, tablets e smartphones. Nossa interface é responsiva e se adapta a diferentes tamanhos de tela, proporcionando uma experiência consistente em todos os dispositivos."
  },
  {
    id: 5,
    category: 'general',
    question: "Como funciona o suporte técnico?",
    answer: "Oferecemos suporte técnico por e-mail, chat e telefone em horário comercial. Nossa equipe de suporte está pronta para ajudar com qualquer dúvida ou problema que você possa ter. Além disso, disponibilizamos uma base de conhecimento completa com tutoriais, vídeos e artigos para ajudar você a aproveitar ao máximo o sistema."
  },
  {
    id: 6,
    category: 'customization',
    question: "O sistema pode ser personalizado para as necessidades específicas da minha clínica?",
    answer: "Sim, o High Tide Systems oferece diversas opções de personalização para atender às necessidades específicas da sua clínica. Você pode configurar campos personalizados, fluxos de trabalho, relatórios e muito mais. Para necessidades mais específicas, nossa equipe de desenvolvimento pode criar soluções sob medida."
  },
  {
    id: 7,
    category: 'pricing',
    question: "Como funciona o modelo de preços?",
    answer: "Oferecemos planos flexíveis baseados no número de profissionais e recursos necessários. Todos os planos incluem atualizações regulares e suporte técnico. Não há taxas de instalação ou configuração, e você pode cancelar a qualquer momento. Entre em contato conosco para obter um orçamento personalizado para sua clínica."
  },
  {
    id: 8,
    category: 'implementation',
    question: "Vocês oferecem treinamento para a equipe?",
    answer: "Sim, oferecemos treinamento completo para toda a equipe da sua clínica. O treinamento pode ser realizado remotamente ou presencialmente, dependendo da sua preferência. Além disso, disponibilizamos materiais de treinamento, vídeos tutoriais e uma base de conhecimento abrangente para consulta a qualquer momento."
  },
  {
    id: 9,
    category: 'technical',
    question: "É possível integrar com outros sistemas que já utilizamos?",
    answer: "Sim, o High Tide Systems oferece APIs e integrações com diversos sistemas, como sistemas contábeis, plataformas de pagamento, sistemas de prontuário eletrônico e muito mais. Nossa equipe técnica pode ajudar a configurar integrações personalizadas para atender às necessidades específicas da sua clínica."
  },
  {
    id: 10,
    category: 'security',
    question: "Como são feitos os backups dos dados?",
    answer: "Realizamos backups automáticos diários de todos os dados do sistema. Os backups são armazenados em servidores redundantes em diferentes localizações geográficas para garantir a máxima segurança. Além disso, você pode solicitar backups manuais a qualquer momento ou configurar a exportação periódica dos seus dados."
  },
  {
    id: 11,
    category: 'pricing',
    question: "Existe um período de teste gratuito?",
    answer: "Sim, oferecemos um período de teste gratuito de 14 dias para que você possa experimentar todas as funcionalidades do sistema antes de tomar uma decisão. Durante esse período, você terá acesso a todos os recursos e ao nosso suporte técnico para ajudar na configuração inicial e responder a quaisquer dúvidas."
  },
  {
    id: 12,
    category: 'customization',
    question: "É possível personalizar os relatórios do sistema?",
    answer: "Sim, o High Tide Systems oferece um poderoso gerador de relatórios que permite criar relatórios personalizados com base em qualquer dado do sistema. Você pode definir filtros, agrupamentos, ordenações e visualizações personalizadas. Os relatórios podem ser exportados em diversos formatos, como PDF, Excel e CSV."
  }
];

const FAQ = () => {
  const [openItems, setOpenItems] = useState([]);
  const [activeCategory, setActiveCategory] = useState('all');

  const toggleItem = (id) => {
    setOpenItems(prev =>
      prev.includes(id)
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  const filteredFaqs = activeCategory === 'all'
    ? faqs
    : faqs.filter(faq => faq.category === activeCategory);

  return (
    <section id="faq" className="py-20 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Perguntas Frequentes
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Tire suas dúvidas sobre o High Tide Systems
          </p>

          {/* Category Tabs */}
          <div className="flex flex-wrap justify-center gap-2 mb-8">
            <button
              onClick={() => setActiveCategory('all')}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                activeCategory === 'all'
                  ? 'bg-primary-500 text-white'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
            >
              Todas
            </button>

            {faqCategories.map(category => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors flex items-center ${
                  activeCategory === category.id
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                <span className={activeCategory === category.id ? 'text-white' : category.color}>
                  {category.icon}
                </span>
                <span className="ml-2">{category.name}</span>
              </button>
            ))}
          </div>
        </motion.div>

        <div className="max-w-4xl mx-auto">
          {filteredFaqs.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">Nenhuma pergunta encontrada nesta categoria.</p>
            </div>
          ) : (
            filteredFaqs.map((faq, index) => {
              const category = faqCategories.find(c => c.id === faq.category);

              return (
                <motion.div
                  key={faq.id}
                  className="mb-4 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <button
                    className="w-full flex items-center justify-between p-6 text-left bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    onClick={() => toggleItem(faq.id)}
                    aria-expanded={openItems.includes(faq.id)}
                  >
                    <div className="flex items-center">
                      <span className={`mr-3 ${category.color}`}>
                        {category.icon}
                      </span>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {faq.question}
                      </h3>
                    </div>
                    <div className={`flex-shrink-0 ml-4 p-1 rounded-full ${
                      openItems.includes(faq.id)
                        ? 'bg-primary-100 dark:bg-primary-900/30'
                        : 'bg-gray-100 dark:bg-gray-700'
                    }`}>
                      {openItems.includes(faq.id) ? (
                        <ChevronUp className={`h-5 w-5 ${openItems.includes(faq.id) ? 'text-primary-500 dark:text-primary-400' : 'text-gray-500 dark:text-gray-400'}`} />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      )}
                    </div>
                  </button>
                  <AnimatePresence>
                    {openItems.includes(faq.id) && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="p-6 pt-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                          <p className="text-gray-600 dark:text-gray-300">
                            {faq.answer}
                          </p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              );
            })
          )}
        </div>

        {/* Additional Help Section */}
        <motion.div
          className="mt-12 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Não encontrou o que procurava?
          </p>
          <a
            href="#contact"
            onClick={(e) => {
              e.preventDefault();
              scrollToElement('contact', 80);
            }}
            className="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors cursor-pointer"
          >
            Entre em contato
          </a>
        </motion.div>
      </div>
    </section>
  );
};

export default FAQ;
