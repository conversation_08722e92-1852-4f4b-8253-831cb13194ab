"use client";

import React, { useState, useEffect } from "react";
import {
  CreditCard,
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Minus,
  Download,
  RefreshCw,
  Loader2,
  Clock,
  DollarSign,
  Package,
  Settings,
  Trash2,
  TrendingUp,
  Shield,
  Zap,
  Crown,
  UserPlus,
  ArrowUpCircle
} from "lucide-react";
import { subscriptionService } from "@/services/subscriptionService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import { api } from "@/utils/api";
import { ModuleHeader, ModuleButton, ModuleModal, ModuleSelect } from "@/components/ui";
import ModuleCard from "@/components/ui/ModuleCard";

const PlansPage = () => {
  const { user } = useAuth();
  const { addToast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [subscription, setSubscription] = useState(null);
  const [plans, setPlans] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showModuleModal, setShowModuleModal] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [selectedModule, setSelectedModule] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState(null);
  const [userStats, setUserStats] = useState({ current: 0, limit: 0 });
  const [customUserCount, setCustomUserCount] = useState(10);

  useEffect(() => {
    if (user?.role === 'SYSTEM_ADMIN') {
      loadCompanies();
    } else {
      loadData();
    }
  }, []);

  useEffect(() => {
    if (selectedCompanyId) {
      loadData();
    }
  }, [selectedCompanyId]);

  const loadCompanies = async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/companies?limit=100');
      setCompanies(response.data.companies || []);
    } catch (error) {
      console.error('Erro ao carregar empresas:', error);
      addToast('Erro ao carregar lista de empresas', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadData = async () => {
    if (user?.role === 'SYSTEM_ADMIN' && !selectedCompanyId) {
      return;
    }

    try {
      setIsLoading(true);

      // Para SYSTEM_ADMIN, adiciona o companyId como query parameter
      const queryParams = user?.role === 'SYSTEM_ADMIN' && selectedCompanyId
        ? `?companyId=${selectedCompanyId}`
        : '';

      // Carregar dados com tratamento individual de erros
      let subscriptionData = null;
      let invoicesData = [];
      let plansData = null;
      let userStatsData = { current: 0, limit: 0 };

      try {
        subscriptionData = await api.get(`/subscription/subscription${queryParams}`).then(res => res.data);
      } catch (error) {
        if (error.response?.status === 404) {
          // Empresa não tem assinatura - isso é normal
          subscriptionData = null;
        } else {
          throw error; // Re-throw outros erros
        }
      }

      try {
        invoicesData = await api.get(`/subscription/invoices${queryParams}`).then(res => res.data);
      } catch (error) {
        if (error.response?.status === 404) {
          // Empresa não tem faturas - isso é normal
          invoicesData = { invoices: [] };
        } else {
          console.warn('Erro ao carregar faturas:', error);
          invoicesData = { invoices: [] };
        }
      }

      try {
        plansData = await subscriptionService.getPlans();
      } catch (error) {
        console.warn('Erro ao carregar planos:', error);
        plansData = null;
      }

      // Carregar estatísticas de usuários
      try {
        const usersResponse = await api.get(`/users${queryParams}&limit=1000`);
        const activeUsers = usersResponse.data.users?.filter(u => u.active) || [];
        userStatsData = {
          current: activeUsers.length,
          limit: subscriptionData?.userLimit || 50 // Usar o limite da subscription
        };
      } catch (error) {
        console.warn('Erro ao carregar estatísticas de usuários:', error);
        // Se houver erro, usar dados da subscription se disponível
        if (subscriptionData?.userLimit) {
          userStatsData.limit = subscriptionData.userLimit;
        }
      }

      setSubscription(subscriptionData);
      setPlans(plansData);
      setInvoices(invoicesData?.invoices || []);
      setUserStats(userStatsData);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      addToast('Erro ao carregar informações do plano', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddModule = async (moduleType) => {
    try {
      setActionLoading(true);
      await subscriptionService.addModule(moduleType);
      addToast('Módulo adicionado com sucesso!', 'success');
      await loadData();
      setShowModuleModal(false);
    } catch (error) {
      console.error('Erro ao adicionar módulo:', error);
      addToast(error.response?.data?.message || 'Erro ao adicionar módulo', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleRemoveModule = async (moduleType) => {
    try {
      setActionLoading(true);
      await subscriptionService.removeModule(moduleType);
      addToast('Módulo removido com sucesso!', 'success');
      await loadData();
    } catch (error) {
      console.error('Erro ao remover módulo:', error);
      addToast(error.response?.data?.message || 'Erro ao remover módulo', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    try {
      setActionLoading(true);
      await subscriptionService.cancelSubscription();
      addToast('Assinatura cancelada com sucesso!', 'success');
      await loadData();
      setShowCancelModal(false);
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      addToast(error.response?.data?.message || 'Erro ao cancelar assinatura', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleUpgradePlan = async (planType, userLimit = null) => {
    try {
      setActionLoading(true);

      // Se não foi especificado userLimit, usar um valor baseado no plano
      const finalUserLimit = userLimit || (planType === 'professional' ? 999 : 9999);

      await subscriptionService.upgradePlan(planType, finalUserLimit);
      addToast(`Plano atualizado para ${planType} com sucesso!`, 'success');
      await loadData();
      setShowUpgradeModal(false);
    } catch (error) {
      console.error('Erro ao fazer upgrade:', error);
      addToast(error.response?.data?.message || 'Erro ao fazer upgrade', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleBuyMoreUsers = async (additionalUsers) => {
    try {
      setActionLoading(true);
      await subscriptionService.addUsers(additionalUsers);
      addToast(`${additionalUsers} usuários adicionais adquiridos!`, 'success');
      await loadData();
      setShowUserModal(false);
    } catch (error) {
      console.error('Erro ao comprar usuários:', error);
      addToast(error.response?.data?.message || 'Erro ao comprar usuários', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600 bg-green-100';
      case 'TRIAL': return 'text-blue-600 bg-blue-100';
      case 'PAST_DUE': return 'text-yellow-600 bg-yellow-100';
      case 'CANCELED': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'ACTIVE': return 'Ativo';
      case 'TRIAL': return 'Período de Teste';
      case 'PAST_DUE': return 'Pagamento em Atraso';
      case 'CANCELED': return 'Cancelado';
      default: return status;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-admin-primary" />
      </div>
    );
  }

  // Se for SYSTEM_ADMIN, mostrar seletor de empresa
  if (user?.role === 'SYSTEM_ADMIN') {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Planos"
          description="Selecione uma empresa para gerenciar sua assinatura, módulos e faturamento"
          icon={<CreditCard size={20} />}
          moduleColor="admin"
        />

        <ModuleCard moduleColor="admin">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Selecionar Empresa
              </h3>
              <ModuleSelect
                value={selectedCompanyId || ''}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                moduleColor="admin"
                className="w-full max-w-md"
              >
                <option value="">Selecione uma empresa...</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>

            {!selectedCompanyId && (
              <div className="text-center py-8">
                <div className="mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4">
                  <AlertCircle className="h-6 w-6 text-admin-primary" />
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  Selecione uma empresa acima para visualizar e gerenciar seus planos de assinatura.
                </p>
              </div>
            )}

            {selectedCompanyId && !subscription && (
              <div className="text-center py-8">
                <div className="mx-auto w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mb-4">
                  <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Nenhuma Assinatura Encontrada
                </h4>
                <p className="text-gray-600 dark:text-gray-400">
                  A empresa selecionada não possui uma assinatura ativa no momento.
                </p>
              </div>
            )}
          </div>
        </ModuleCard>

        {/* Se há assinatura selecionada, mostrar o conteúdo normal */}
        {selectedCompanyId && subscription && (
          <>
            {/* Status da Assinatura para SYSTEM_ADMIN */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Card Principal da Assinatura */}
              <ModuleCard moduleColor="admin" className="lg:col-span-2">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-admin-primary/10 rounded-lg flex items-center justify-center">
                      <Crown className="h-5 w-5 text-admin-primary" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        Plano da Empresa
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {subscription.billingCycle === 'YEARLY' ? 'Faturamento Anual' : 'Faturamento Mensal'}
                      </p>
                    </div>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                    {getStatusText(subscription.status)}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Valor Mensal</p>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {formatCurrency(subscription.pricePerMonth)}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Próximo Pagamento</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Data de Início</p>
                      <p className="font-medium text-gray-900 dark:text-white">
                        {formatDate(subscription.startDate)}
                      </p>
                    </div>
                  </div>
                </div>

                {subscription.cancelAtPeriodEnd && (
                  <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="flex items-center">
                      <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3" />
                      <div>
                        <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                          Cancelamento Agendado
                        </p>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300">
                          A assinatura desta empresa será cancelada no final do período atual.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </ModuleCard>

              {/* Card de Usuários */}
              <ModuleCard moduleColor="admin">
                <div className="text-center">
                  <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4">
                    <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Usuários
                  </h3>
                  <div className="mb-4">
                    <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                      {userStats.current}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      de {userStats.limit} disponíveis
                    </p>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min((userStats.current / userStats.limit) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </ModuleCard>

              {/* Card de Módulos */}
              <ModuleCard moduleColor="admin">
                <div className="text-center">
                  <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4">
                    <Package className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Módulos
                  </h3>
                  <p className="text-3xl font-bold text-green-600 dark:text-green-400 mb-4">
                    {subscription.modules?.filter(m => m.active).length || 0}
                  </p>
                  <ModuleButton
                    variant="outline"
                    size="sm"
                    onClick={() => setShowModuleModal(true)}
                    moduleColor="admin"
                    className="w-full"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Gerenciar
                  </ModuleButton>
                </div>
              </ModuleCard>
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ModuleHeader
        title="Gerenciamento de Planos"
        description="Gerencie sua assinatura, módulos e faturamento"
        icon={<CreditCard size={20} />}
        moduleColor="admin"
      />

      {/* Status da Assinatura */}
      {subscription && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Card Principal da Assinatura */}
          <ModuleCard moduleColor="admin" className="lg:col-span-2">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-admin-primary/10 rounded-lg flex items-center justify-center">
                  <Crown className="h-5 w-5 text-admin-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Plano Atual
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {subscription.billingCycle === 'YEARLY' ? 'Faturamento Anual' : 'Faturamento Mensal'}
                  </p>
                </div>
              </div>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                {getStatusText(subscription.status)}
              </span>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Valor Mensal</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(subscription.pricePerMonth)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Próximo Pagamento</p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'}
                  </p>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Data de Início</p>
                  <p className="font-medium text-gray-900 dark:text-white">
                    {formatDate(subscription.startDate)}
                  </p>
                </div>
                <div>
                  <ModuleButton
                    variant="outline"
                    size="sm"
                    onClick={() => setShowUpgradeModal(true)}
                    moduleColor="admin"
                    className="w-full"
                  >
                    <ArrowUpCircle className="h-4 w-4 mr-2" />
                    Fazer Upgrade
                  </ModuleButton>
                </div>
              </div>
            </div>

            {subscription.cancelAtPeriodEnd && (
              <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                      Cancelamento Agendado
                    </p>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      Sua assinatura será cancelada no final do período atual.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </ModuleCard>

          {/* Card de Usuários */}
          <ModuleCard moduleColor="admin">
            <div className="text-center">
              <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Usuários
              </h3>
              <div className="mb-4">
                <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {userStats.current}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  de {userStats.limit} disponíveis
                </p>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min((userStats.current / userStats.limit) * 100, 100)}%` }}
                ></div>
              </div>
              <ModuleButton
                variant="outline"
                size="sm"
                onClick={() => setShowUserModal(true)}
                moduleColor="admin"
                className="w-full"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Comprar Usuários
              </ModuleButton>
            </div>
          </ModuleCard>

          {/* Card de Módulos */}
          <ModuleCard moduleColor="admin">
            <div className="text-center">
              <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mb-4">
                <Package className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Módulos
              </h3>
              <p className="text-3xl font-bold text-green-600 dark:text-green-400 mb-4">
                {subscription.modules?.filter(m => m.active).length || 0}
              </p>
              <ModuleButton
                variant="outline"
                size="sm"
                onClick={() => setShowModuleModal(true)}
                moduleColor="admin"
                className="w-full"
              >
                <Settings className="h-4 w-4 mr-2" />
                Gerenciar
              </ModuleButton>
            </div>
          </ModuleCard>
        </div>
      )}

      {/* Módulos */}
      {subscription?.modules && (
        <ModuleCard moduleColor="admin">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Módulos da Assinatura
            </h3>
            <ModuleButton
              onClick={() => setShowModuleModal(true)}
              moduleColor="admin"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Módulo
            </ModuleButton>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {subscription.modules.map((module) => (
              <div
                key={module.id}
                className={`p-4 border rounded-lg ${
                  module.active
                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                    : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {plans?.modules?.[module.moduleType]?.name || module.moduleType}
                  </h4>
                  {module.active ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {formatCurrency(module.pricePerMonth)}/mês
                </p>
                {module.active && !['BASIC', 'ADMIN', 'SCHEDULING'].includes(module.moduleType) && (
                  <ModuleButton
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveModule(module.moduleType)}
                    disabled={actionLoading}
                    className="w-full text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Minus className="h-4 w-4 mr-2" />
                    Remover
                  </ModuleButton>
                )}
              </div>
            ))}
          </div>
        </ModuleCard>
      )}

      {/* Histórico de Faturas */}
      {invoices.length > 0 && (
        <ModuleCard moduleColor="admin">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Histórico de Faturas
            </h3>
            <ModuleButton
              variant="outline"
              size="sm"
              onClick={loadData}
              disabled={isLoading}
              moduleColor="admin"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Atualizar
            </ModuleButton>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Data
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Valor
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Status
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Vencimento
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody>
                {invoices.map((invoice) => (
                  <tr key={invoice.id} className="border-b border-gray-100 dark:border-gray-800">
                    <td className="py-3 px-4 text-gray-900 dark:text-white">
                      {formatDate(invoice.createdAt)}
                    </td>
                    <td className="py-3 px-4 text-gray-900 dark:text-white">
                      {formatCurrency(invoice.amount)}
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        invoice.status === 'PAID'
                          ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20'
                          : invoice.status === 'PENDING'
                          ? 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20'
                          : 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
                      }`}>
                        {invoice.status === 'PAID' ? 'Pago' :
                         invoice.status === 'PENDING' ? 'Pendente' : 'Falhou'}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-gray-900 dark:text-white">
                      {formatDate(invoice.dueDate)}
                    </td>
                    <td className="py-3 px-4 text-right">
                      {invoice.stripeInvoiceUrl && (
                        <a
                          href={invoice.stripeInvoiceUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-admin-primary hover:text-admin-primary/80"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </a>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </ModuleCard>
      )}

      {/* Ações Rápidas */}
      {subscription && subscription.status === 'ACTIVE' && (
        <ModuleCard moduleColor="admin">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Ações Rápidas
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Gerencie sua assinatura e recursos
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Upgrade de Plano */}
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-admin-primary/30 transition-colors">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-admin-primary/10 rounded-lg flex items-center justify-center">
                  <TrendingUp className="h-4 w-4 text-admin-primary" />
                </div>
                <h4 className="font-medium text-gray-900 dark:text-white">Upgrade de Plano</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Acesse recursos avançados e aumente sua capacidade
              </p>
              <ModuleButton
                variant="outline"
                size="sm"
                onClick={() => setShowUpgradeModal(true)}
                moduleColor="admin"
                className="w-full"
              >
                <ArrowUpCircle className="h-4 w-4 mr-2" />
                Fazer Upgrade
              </ModuleButton>
            </div>

            {/* Comprar Usuários */}
            <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-blue-300 transition-colors">
              <div className="flex items-center space-x-3 mb-3">
                <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center">
                  <UserPlus className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <h4 className="font-medium text-gray-900 dark:text-white">Mais Usuários</h4>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                Adicione mais usuários ao seu plano atual
              </p>
              <ModuleButton
                variant="outline"
                size="sm"
                onClick={() => setShowUserModal(true)}
                className="w-full border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/20"
              >
                <Plus className="h-4 w-4 mr-2" />
                Comprar Usuários
              </ModuleButton>
            </div>

            {/* Cancelar Assinatura */}
            {!subscription.cancelAtPeriodEnd && (
              <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:border-red-300 transition-colors">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
                    <Trash2 className="h-4 w-4 text-red-600 dark:text-red-400" />
                  </div>
                  <h4 className="font-medium text-gray-900 dark:text-white">Cancelar Plano</h4>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  Cancele sua assinatura a qualquer momento
                </p>
                <ModuleButton
                  variant="outline"
                  size="sm"
                  onClick={() => setShowCancelModal(true)}
                  className="w-full text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Cancelar
                </ModuleButton>
              </div>
            )}
          </div>
        </ModuleCard>
      )}

      {/* Modal de Cancelamento */}
      <ModuleModal
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        title="Cancelar Assinatura"
        moduleColor="admin"
      >
        <div className="space-y-4">
          <div className="flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Atenção!
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                Ao cancelar, você perderá acesso aos recursos pagos no final do período atual.
              </p>
            </div>
          </div>

          <p className="text-gray-600 dark:text-gray-400">
            Tem certeza de que deseja cancelar sua assinatura? Esta ação não pode ser desfeita.
          </p>

          <div className="flex justify-end space-x-3">
            <ModuleButton
              variant="outline"
              onClick={() => setShowCancelModal(false)}
              disabled={actionLoading}
              moduleColor="admin"
            >
              Manter Assinatura
            </ModuleButton>
            <ModuleButton
              onClick={handleCancelSubscription}
              disabled={actionLoading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {actionLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Cancelar Assinatura
            </ModuleButton>
          </div>
        </div>
      </ModuleModal>

      {/* Modal de Módulos */}
      <ModuleModal
        isOpen={showModuleModal}
        onClose={() => setShowModuleModal(false)}
        title="Gerenciar Módulos"
        moduleColor="admin"
        size="lg"
      >
        <div className="space-y-6">
          <p className="text-gray-600 dark:text-gray-400">
            Adicione ou remova módulos da sua assinatura. Os módulos básicos não podem ser removidos.
          </p>

          {plans?.modules && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(plans.modules).map(([moduleType, moduleInfo]) => {
                const isActive = subscription?.modules?.find(m => m.moduleType === moduleType && m.active);
                const isBasic = ['BASIC', 'ADMIN', 'SCHEDULING'].includes(moduleType);

                return (
                  <div
                    key={moduleType}
                    className={`p-4 border rounded-lg ${
                      isActive
                        ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                        : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {moduleInfo.name}
                      </h4>
                      {isActive && <CheckCircle className="h-5 w-5 text-green-600" />}
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {moduleInfo.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(moduleInfo.monthlyPrice)}/mês
                      </span>

                      {!isBasic && (
                        <ModuleButton
                          size="sm"
                          variant={isActive ? "outline" : "default"}
                          onClick={() => isActive ? handleRemoveModule(moduleType) : handleAddModule(moduleType)}
                          disabled={actionLoading}
                          moduleColor="admin"
                          className={isActive ? "text-red-600 border-red-200 hover:bg-red-50" : ""}
                        >
                          {actionLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : isActive ? (
                            <>
                              <Minus className="h-4 w-4 mr-1" />
                              Remover
                            </>
                          ) : (
                            <>
                              <Plus className="h-4 w-4 mr-1" />
                              Adicionar
                            </>
                          )}
                        </ModuleButton>
                      )}

                      {isBasic && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          Incluído
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </ModuleModal>

      {/* Modal de Upgrade */}
      <ModuleModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        title="Upgrade de Plano"
        moduleColor="admin"
        size="lg"
      >
        <div className="space-y-6">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4">
              <Crown className="h-8 w-8 text-admin-primary" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Faça Upgrade do Seu Plano
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Desbloqueie recursos avançados e aumente sua capacidade
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-6 border-2 border-admin-primary/20 rounded-lg bg-admin-primary/5">
              <div className="flex items-center space-x-3 mb-4">
                <Shield className="h-6 w-6 text-admin-primary" />
                <h4 className="font-semibold text-gray-900 dark:text-white">Plano Profissional</h4>
              </div>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-6">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Usuários ilimitados
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Todos os módulos inclusos
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Suporte prioritário
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Relatórios avançados
                </li>
              </ul>
              <div className="text-center">
                <p className="text-2xl font-bold text-admin-primary mb-2">R$ 299,90</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">por mês</p>
              </div>
            </div>

            <div className="p-6 border-2 border-purple-200 rounded-lg bg-purple-50 dark:bg-purple-900/10">
              <div className="flex items-center space-x-3 mb-4">
                <Zap className="h-6 w-6 text-purple-600" />
                <h4 className="font-semibold text-gray-900 dark:text-white">Plano Enterprise</h4>
              </div>
              <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400 mb-6">
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Tudo do Profissional
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  API personalizada
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Integração dedicada
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                  Gerente de conta
                </li>
              </ul>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600 mb-2">R$ 599,90</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">por mês</p>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <ModuleButton
              variant="outline"
              onClick={() => setShowUpgradeModal(false)}
              disabled={actionLoading}
              moduleColor="admin"
            >
              Cancelar
            </ModuleButton>
            <ModuleButton
              onClick={() => handleUpgradePlan('professional')}
              disabled={actionLoading}
              moduleColor="admin"
            >
              {actionLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <ArrowUpCircle className="h-4 w-4 mr-2" />
              )}
              Escolher Profissional
            </ModuleButton>
            <ModuleButton
              onClick={() => handleUpgradePlan('enterprise')}
              disabled={actionLoading}
              moduleColor="admin"
              className="ml-2"
            >
              {actionLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <ArrowUpCircle className="h-4 w-4 mr-2" />
              )}
              Escolher Enterprise
            </ModuleButton>
          </div>
        </div>
      </ModuleModal>

      {/* Modal de Compra de Usuários */}
      <ModuleModal
        isOpen={showUserModal}
        onClose={() => setShowUserModal(false)}
        title="Comprar Mais Usuários"
        moduleColor="admin"
      >
        <div className="space-y-6">
          <div className="text-center">
            <div className="mx-auto w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center mb-4">
              <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Adicionar Usuários
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Expanda sua equipe com usuários adicionais
            </p>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
            <div className="flex justify-between items-center mb-4">
              <span className="text-sm text-gray-600 dark:text-gray-400">Usuários atuais:</span>
              <span className="font-semibold text-gray-900 dark:text-white">{userStats.current}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Limite atual:</span>
              <span className="font-semibold text-gray-900 dark:text-white">{userStats.limit}</span>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-4">Pacotes Rápidos:</h4>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { users: 10, popular: false },
                  { users: 25, popular: true },
                  { users: 50, popular: false }
                ].map((pack) => {
                  // Calcular preço dinamicamente
                  const basePrice = 19.90;
                  const getDiscountByUserCount = (users) => {
                    if (users >= 200) return 40;
                    if (users >= 100) return 35;
                    if (users >= 50) return 25;
                    if (users >= 20) return 15;
                    if (users >= 5) return 10;
                    return 0;
                  };

                  const discount = getDiscountByUserCount(pack.users);
                  const totalWithoutDiscount = pack.users * basePrice;
                  const discountAmount = totalWithoutDiscount * (discount / 100);
                  const finalPrice = totalWithoutDiscount - discountAmount;

                  return (
                    <div
                      key={pack.users}
                      className={`relative p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        pack.popular
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'border-gray-200 dark:border-gray-700 hover:border-blue-300'
                      }`}
                      onClick={() => handleBuyMoreUsers(pack.users)}
                    >
                      {pack.popular && (
                        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                          <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                            Mais Popular
                          </span>
                        </div>
                      )}
                      <div className="text-center">
                        <p className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                          +{pack.users}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">usuários</p>
                        <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                          {formatCurrency(finalPrice)}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">por mês</p>
                        {discount > 0 && (
                          <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                            {discount}% de desconto
                          </p>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="font-medium text-gray-900 dark:text-white mb-4">Quantidade Personalizada:</h4>

              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setCustomUserCount(Math.max(1, customUserCount - 1))}
                    className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300"
                  >
                    -
                  </button>
                  <input
                    type="number"
                    min="1"
                    max="500"
                    value={customUserCount}
                    onChange={(e) => setCustomUserCount(Math.max(1, parseInt(e.target.value) || 1))}
                    className="w-20 text-center text-lg font-semibold text-gray-900 dark:text-white bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => setCustomUserCount(Math.min(500, customUserCount + 1))}
                    className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center text-gray-700 dark:text-gray-300"
                  >
                    +
                  </button>
                </div>

                <div className="flex-1">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Preço estimado: <span className="font-semibold text-blue-600 dark:text-blue-400">
                      {(() => {
                        const basePrice = 19.90;
                        const getDiscountByUserCount = (users) => {
                          if (users >= 200) return 40;
                          if (users >= 100) return 35;
                          if (users >= 50) return 25;
                          if (users >= 20) return 15;
                          if (users >= 5) return 10;
                          return 0;
                        };

                        const discount = getDiscountByUserCount(customUserCount);
                        const totalWithoutDiscount = customUserCount * basePrice;
                        const discountAmount = totalWithoutDiscount * (discount / 100);
                        const finalPrice = totalWithoutDiscount - discountAmount;

                        return formatCurrency(finalPrice);
                      })()}
                    </span>/mês
                  </div>
                </div>
              </div>

              <ModuleButton
                onClick={() => handleBuyMoreUsers(customUserCount)}
                disabled={actionLoading}
                moduleColor="admin"
                className="w-full"
              >
                {actionLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Plus className="h-4 w-4 mr-2" />
                )}
                Comprar {customUserCount} Usuários
              </ModuleButton>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <ModuleButton
              variant="outline"
              onClick={() => setShowUserModal(false)}
              disabled={actionLoading}
              moduleColor="admin"
            >
              Fechar
            </ModuleButton>
          </div>
        </div>
      </ModuleModal>
    </div>
  );
};

export default PlansPage;
