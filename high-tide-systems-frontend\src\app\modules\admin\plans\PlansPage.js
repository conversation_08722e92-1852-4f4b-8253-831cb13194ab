"use client";

import React, { useState, useEffect } from "react";
import {
  CreditCard,
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Minus,
  Download,
  RefreshCw,
  Loader2,
  Clock,
  DollarSign,
  Package,
  Settings,
  Trash2
} from "lucide-react";
import { subscriptionService } from "@/services/subscriptionService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import { api } from "@/utils/api";
import { ModuleHeader, ModuleButton, ModuleModal, ModuleSelect } from "@/components/ui";
import ModuleCard from "@/components/ui/ModuleCard";

const PlansPage = () => {
  const { user } = useAuth();
  const { addToast } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [subscription, setSubscription] = useState(null);
  const [plans, setPlans] = useState(null);
  const [invoices, setInvoices] = useState([]);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showModuleModal, setShowModuleModal] = useState(false);
  const [selectedModule, setSelectedModule] = useState(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState(null);

  useEffect(() => {
    if (user?.role === 'SYSTEM_ADMIN') {
      loadCompanies();
    } else {
      loadData();
    }
  }, []);

  useEffect(() => {
    if (selectedCompanyId) {
      loadData();
    }
  }, [selectedCompanyId]);

  const loadCompanies = async () => {
    try {
      setIsLoading(true);
      const response = await api.get('/companies?limit=100');
      setCompanies(response.data.companies || []);
    } catch (error) {
      console.error('Erro ao carregar empresas:', error);
      addToast('Erro ao carregar lista de empresas', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadData = async () => {
    if (user?.role === 'SYSTEM_ADMIN' && !selectedCompanyId) {
      return;
    }

    try {
      setIsLoading(true);

      // Para SYSTEM_ADMIN, adiciona o companyId como query parameter
      const queryParams = user?.role === 'SYSTEM_ADMIN' && selectedCompanyId
        ? `?companyId=${selectedCompanyId}`
        : '';

      // Carregar dados com tratamento individual de erros
      let subscriptionData = null;
      let invoicesData = [];
      let plansData = null;

      try {
        subscriptionData = await api.get(`/subscription/subscription${queryParams}`).then(res => res.data);
      } catch (error) {
        if (error.response?.status === 404) {
          // Empresa não tem assinatura - isso é normal
          subscriptionData = null;
        } else {
          throw error; // Re-throw outros erros
        }
      }

      try {
        invoicesData = await api.get(`/subscription/invoices${queryParams}`).then(res => res.data);
      } catch (error) {
        if (error.response?.status === 404) {
          // Empresa não tem faturas - isso é normal
          invoicesData = { invoices: [] };
        } else {
          console.warn('Erro ao carregar faturas:', error);
          invoicesData = { invoices: [] };
        }
      }

      try {
        plansData = await subscriptionService.getPlans();
      } catch (error) {
        console.warn('Erro ao carregar planos:', error);
        plansData = null;
      }

      setSubscription(subscriptionData);
      setPlans(plansData);
      setInvoices(invoicesData?.invoices || []);
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      addToast('Erro ao carregar informações do plano', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddModule = async (moduleType) => {
    try {
      setActionLoading(true);
      await subscriptionService.addModule(moduleType);
      addToast('Módulo adicionado com sucesso!', 'success');
      await loadData();
      setShowModuleModal(false);
    } catch (error) {
      console.error('Erro ao adicionar módulo:', error);
      addToast(error.response?.data?.message || 'Erro ao adicionar módulo', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleRemoveModule = async (moduleType) => {
    try {
      setActionLoading(true);
      await subscriptionService.removeModule(moduleType);
      addToast('Módulo removido com sucesso!', 'success');
      await loadData();
    } catch (error) {
      console.error('Erro ao remover módulo:', error);
      addToast(error.response?.data?.message || 'Erro ao remover módulo', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    try {
      setActionLoading(true);
      await subscriptionService.cancelSubscription();
      addToast('Assinatura cancelada com sucesso!', 'success');
      await loadData();
      setShowCancelModal(false);
    } catch (error) {
      console.error('Erro ao cancelar assinatura:', error);
      addToast(error.response?.data?.message || 'Erro ao cancelar assinatura', 'error');
    } finally {
      setActionLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ACTIVE': return 'text-green-600 bg-green-100';
      case 'TRIAL': return 'text-blue-600 bg-blue-100';
      case 'PAST_DUE': return 'text-yellow-600 bg-yellow-100';
      case 'CANCELED': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'ACTIVE': return 'Ativo';
      case 'TRIAL': return 'Período de Teste';
      case 'PAST_DUE': return 'Pagamento em Atraso';
      case 'CANCELED': return 'Cancelado';
      default: return status;
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-admin-primary" />
      </div>
    );
  }

  // Se for SYSTEM_ADMIN, mostrar seletor de empresa
  if (user?.role === 'SYSTEM_ADMIN') {
    return (
      <div className="space-y-6">
        <ModuleHeader
          title="Gerenciamento de Planos"
          description="Selecione uma empresa para gerenciar sua assinatura, módulos e faturamento"
          icon={<CreditCard size={20} />}
          moduleColor="admin"
        />

        <ModuleCard moduleColor="admin">
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Selecionar Empresa
              </h3>
              <ModuleSelect
                value={selectedCompanyId || ''}
                onChange={(e) => setSelectedCompanyId(e.target.value)}
                moduleColor="admin"
                className="w-full max-w-md"
              >
                <option value="">Selecione uma empresa...</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>

            {!selectedCompanyId && (
              <div className="text-center py-8">
                <div className="mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4">
                  <AlertCircle className="h-6 w-6 text-admin-primary" />
                </div>
                <p className="text-gray-600 dark:text-gray-400">
                  Selecione uma empresa acima para visualizar e gerenciar seus planos de assinatura.
                </p>
              </div>
            )}

            {selectedCompanyId && !subscription && (
              <div className="text-center py-8">
                <div className="mx-auto w-12 h-12 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mb-4">
                  <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                </div>
                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Nenhuma Assinatura Encontrada
                </h4>
                <p className="text-gray-600 dark:text-gray-400">
                  A empresa selecionada não possui uma assinatura ativa no momento.
                </p>
              </div>
            )}
          </div>
        </ModuleCard>

        {/* Se há assinatura selecionada, mostrar o conteúdo normal */}
        {selectedCompanyId && subscription && (
          <>
            {/* Status da Assinatura */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <ModuleCard moduleColor="admin" className="lg:col-span-2">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Status da Assinatura
                  </h3>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                    {getStatusText(subscription.status)}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Ciclo de Faturamento</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {subscription.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Valor Mensal</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatCurrency(subscription.pricePerMonth)}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Próximo Pagamento</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Data de Início</p>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {formatDate(subscription.startDate)}
                    </p>
                  </div>
                </div>

                {subscription.cancelAtPeriodEnd && (
                  <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="flex items-center">
                      <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                      <p className="text-sm text-yellow-800 dark:text-yellow-200">
                        A assinatura desta empresa será cancelada no final do período atual.
                      </p>
                    </div>
                  </div>
                )}
              </ModuleCard>

              <ModuleCard moduleColor="admin">
                <div className="text-center">
                  <div className="mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4">
                    <Package className="h-6 w-6 text-admin-primary" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Módulos Ativos
                  </h3>
                  <p className="text-3xl font-bold text-admin-primary mb-4">
                    {subscription.modules?.filter(m => m.active).length || 0}
                  </p>
                  <ModuleButton
                    variant="outline"
                    size="sm"
                    onClick={() => setShowModuleModal(true)}
                    moduleColor="admin"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Gerenciar Módulos
                  </ModuleButton>
                </div>
              </ModuleCard>
            </div>
          </>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <ModuleHeader
        title="Gerenciamento de Planos"
        description="Gerencie sua assinatura, módulos e faturamento"
        icon={<CreditCard size={20} />}
        moduleColor="admin"
      />

      {/* Status da Assinatura */}
      {subscription && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <ModuleCard moduleColor="admin" className="lg:col-span-2">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Status da Assinatura
              </h3>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(subscription.status)}`}>
                {getStatusText(subscription.status)}
              </span>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Ciclo de Faturamento</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {subscription.billingCycle === 'YEARLY' ? 'Anual' : 'Mensal'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Valor Mensal</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {formatCurrency(subscription.pricePerMonth)}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Próximo Pagamento</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Data de Início</p>
                <p className="font-medium text-gray-900 dark:text-white">
                  {formatDate(subscription.startDate)}
                </p>
              </div>
            </div>

            {subscription.cancelAtPeriodEnd && (
              <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-2" />
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    Sua assinatura será cancelada no final do período atual.
                  </p>
                </div>
              </div>
            )}
          </ModuleCard>

          <ModuleCard moduleColor="admin">
            <div className="text-center">
              <div className="mx-auto w-12 h-12 bg-admin-primary/10 rounded-full flex items-center justify-center mb-4">
                <Package className="h-6 w-6 text-admin-primary" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Módulos Ativos
              </h3>
              <p className="text-3xl font-bold text-admin-primary mb-4">
                {subscription.modules?.filter(m => m.active).length || 0}
              </p>
              <ModuleButton
                variant="outline"
                size="sm"
                onClick={() => setShowModuleModal(true)}
                moduleColor="admin"
              >
                <Settings className="h-4 w-4 mr-2" />
                Gerenciar Módulos
              </ModuleButton>
            </div>
          </ModuleCard>
        </div>
      )}

      {/* Módulos */}
      {subscription?.modules && (
        <ModuleCard moduleColor="admin">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Módulos da Assinatura
            </h3>
            <ModuleButton
              onClick={() => setShowModuleModal(true)}
              moduleColor="admin"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar Módulo
            </ModuleButton>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {subscription.modules.map((module) => (
              <div
                key={module.id}
                className={`p-4 border rounded-lg ${
                  module.active
                    ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                    : 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 dark:text-white">
                    {plans?.modules?.[module.moduleType]?.name || module.moduleType}
                  </h4>
                  {module.active ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <XCircle className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {formatCurrency(module.pricePerMonth)}/mês
                </p>
                {module.active && !['BASIC', 'ADMIN', 'SCHEDULING'].includes(module.moduleType) && (
                  <ModuleButton
                    variant="outline"
                    size="sm"
                    onClick={() => handleRemoveModule(module.moduleType)}
                    disabled={actionLoading}
                    className="w-full text-red-600 border-red-200 hover:bg-red-50"
                  >
                    <Minus className="h-4 w-4 mr-2" />
                    Remover
                  </ModuleButton>
                )}
              </div>
            ))}
          </div>
        </ModuleCard>
      )}

      {/* Histórico de Faturas */}
      {invoices.length > 0 && (
        <ModuleCard moduleColor="admin">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Histórico de Faturas
            </h3>
            <ModuleButton
              variant="outline"
              size="sm"
              onClick={loadData}
              disabled={isLoading}
              moduleColor="admin"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Atualizar
            </ModuleButton>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Data
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Valor
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Status
                  </th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Vencimento
                  </th>
                  <th className="text-right py-3 px-4 font-medium text-gray-900 dark:text-white">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody>
                {invoices.map((invoice) => (
                  <tr key={invoice.id} className="border-b border-gray-100 dark:border-gray-800">
                    <td className="py-3 px-4 text-gray-900 dark:text-white">
                      {formatDate(invoice.createdAt)}
                    </td>
                    <td className="py-3 px-4 text-gray-900 dark:text-white">
                      {formatCurrency(invoice.amount)}
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        invoice.status === 'PAID'
                          ? 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20'
                          : invoice.status === 'PENDING'
                          ? 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20'
                          : 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20'
                      }`}>
                        {invoice.status === 'PAID' ? 'Pago' :
                         invoice.status === 'PENDING' ? 'Pendente' : 'Falhou'}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-gray-900 dark:text-white">
                      {formatDate(invoice.dueDate)}
                    </td>
                    <td className="py-3 px-4 text-right">
                      {invoice.stripeInvoiceUrl && (
                        <a
                          href={invoice.stripeInvoiceUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-admin-primary hover:text-admin-primary/80"
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </a>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </ModuleCard>
      )}

      {/* Ações da Assinatura */}
      {subscription && subscription.status === 'ACTIVE' && !subscription.cancelAtPeriodEnd && (
        <ModuleCard moduleColor="admin">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Gerenciar Assinatura
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Cancele sua assinatura ou faça alterações no seu plano
              </p>
            </div>
            <ModuleButton
              variant="outline"
              onClick={() => setShowCancelModal(true)}
              className="text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Cancelar Assinatura
            </ModuleButton>
          </div>
        </ModuleCard>
      )}

      {/* Modal de Cancelamento */}
      <ModuleModal
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        title="Cancelar Assinatura"
        moduleColor="admin"
      >
        <div className="space-y-4">
          <div className="flex items-center p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <AlertCircle className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mr-3" />
            <div>
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Atenção!
              </p>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                Ao cancelar, você perderá acesso aos recursos pagos no final do período atual.
              </p>
            </div>
          </div>

          <p className="text-gray-600 dark:text-gray-400">
            Tem certeza de que deseja cancelar sua assinatura? Esta ação não pode ser desfeita.
          </p>

          <div className="flex justify-end space-x-3">
            <ModuleButton
              variant="outline"
              onClick={() => setShowCancelModal(false)}
              disabled={actionLoading}
              moduleColor="admin"
            >
              Manter Assinatura
            </ModuleButton>
            <ModuleButton
              onClick={handleCancelSubscription}
              disabled={actionLoading}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              {actionLoading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Trash2 className="h-4 w-4 mr-2" />
              )}
              Cancelar Assinatura
            </ModuleButton>
          </div>
        </div>
      </ModuleModal>

      {/* Modal de Módulos */}
      <ModuleModal
        isOpen={showModuleModal}
        onClose={() => setShowModuleModal(false)}
        title="Gerenciar Módulos"
        moduleColor="admin"
        size="lg"
      >
        <div className="space-y-6">
          <p className="text-gray-600 dark:text-gray-400">
            Adicione ou remova módulos da sua assinatura. Os módulos básicos não podem ser removidos.
          </p>

          {plans?.modules && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(plans.modules).map(([moduleType, moduleInfo]) => {
                const isActive = subscription?.modules?.find(m => m.moduleType === moduleType && m.active);
                const isBasic = ['BASIC', 'ADMIN', 'SCHEDULING'].includes(moduleType);

                return (
                  <div
                    key={moduleType}
                    className={`p-4 border rounded-lg ${
                      isActive
                        ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                        : 'border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900 dark:text-white">
                        {moduleInfo.name}
                      </h4>
                      {isActive && <CheckCircle className="h-5 w-5 text-green-600" />}
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                      {moduleInfo.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {formatCurrency(moduleInfo.monthlyPrice)}/mês
                      </span>

                      {!isBasic && (
                        <ModuleButton
                          size="sm"
                          variant={isActive ? "outline" : "default"}
                          onClick={() => isActive ? handleRemoveModule(moduleType) : handleAddModule(moduleType)}
                          disabled={actionLoading}
                          moduleColor="admin"
                          className={isActive ? "text-red-600 border-red-200 hover:bg-red-50" : ""}
                        >
                          {actionLoading ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : isActive ? (
                            <>
                              <Minus className="h-4 w-4 mr-1" />
                              Remover
                            </>
                          ) : (
                            <>
                              <Plus className="h-4 w-4 mr-1" />
                              Adicionar
                            </>
                          )}
                        </ModuleButton>
                      )}

                      {isBasic && (
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          Incluído
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </ModuleModal>
    </div>
  );
};

export default PlansPage;
